<?php
/**
 * ملف تصحيح الطلبات المتروكة
 * ضع هذا الملف في جذر الموقع وقم بتشغيله لفحص المشكلة
 */

// تحميل WordPress
require_once('wp-config.php');

echo "<h1>فحص الطلبات المتروكة</h1>";

// فحص الطلبات الحديثة
global $wpdb;

echo "<h2>الطلبات الحديثة (آخر ساعة)</h2>";
$recent_orders = $wpdb->get_results("
    SELECT p.ID, p.post_status, p.post_date, pm.meta_value as phone
    FROM {$wpdb->posts} p
    LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_customer_phone'
    WHERE p.post_type = 'shop_order'
    AND p.post_date > '" . date('Y-m-d H:i:s', time() - HOUR_IN_SECONDS) . "'
    ORDER BY p.post_date DESC
");

if ($recent_orders) {
    echo "<table border='1'>";
    echo "<tr><th>معرف الطلب</th><th>الحالة</th><th>التاريخ</th><th>الهاتف</th></tr>";
    foreach ($recent_orders as $order) {
        echo "<tr>";
        echo "<td>#{$order->ID}</td>";
        echo "<td>{$order->post_status}</td>";
        echo "<td>{$order->post_date}</td>";
        echo "<td>{$order->phone}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد طلبات حديثة</p>";
}

echo "<h2>مفاتيح المسودات الموجودة</h2>";
$draft_options = $wpdb->get_results("
    SELECT option_name, option_value 
    FROM {$wpdb->options} 
    WHERE option_name LIKE 'pexlat_form_draft_%'
    ORDER BY option_name
");

if ($draft_options) {
    echo "<table border='1'>";
    echo "<tr><th>المفتاح</th><th>معرف الطلب</th><th>الهاتف</th><th>تاريخ الإنشاء</th></tr>";
    foreach ($draft_options as $option) {
        $data = maybe_unserialize($option->option_value);
        echo "<tr>";
        echo "<td>{$option->option_name}</td>";
        echo "<td>#{$data['order_id']}</td>";
        echo "<td>" . ($data['phone'] ?? 'غير محدد') . "</td>";
        echo "<td>" . ($data['created'] ?? 'غير محدد') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد مفاتيح مسودات</p>";
}

echo "<h2>فحص طلب معين</h2>";
if (isset($_GET['order_id'])) {
    $order_id = intval($_GET['order_id']);
    $order = wc_get_order($order_id);
    
    if ($order) {
        echo "<p><strong>معرف الطلب:</strong> #{$order->get_id()}</p>";
        echo "<p><strong>الحالة:</strong> {$order->get_status()}</p>";
        echo "<p><strong>تاريخ الإنشاء:</strong> {$order->get_date_created()->date('Y-m-d H:i:s')}</p>";
        echo "<p><strong>الهاتف:</strong> {$order->get_meta('_customer_phone')}</p>";
        echo "<p><strong>الاسم:</strong> {$order->get_billing_first_name()} {$order->get_billing_last_name()}</p>";
        
        $items = $order->get_items();
        if ($items) {
            echo "<h3>المنتجات:</h3>";
            foreach ($items as $item) {
                echo "<p>- {$item->get_name()} (الكمية: {$item->get_quantity()})</p>";
            }
        }
    } else {
        echo "<p>الطلب غير موجود</p>";
    }
}

echo "<form method='get'>";
echo "<p>فحص طلب: <input type='number' name='order_id' placeholder='معرف الطلب'> <input type='submit' value='فحص'></p>";
echo "</form>";

echo "<h2>تنظيف المسودات القديمة</h2>";
if (isset($_GET['cleanup'])) {
    // تشغيل تنظيف المسودات
    do_action('pexlat_form_cleanup_old_drafts');
    echo "<p style='color: green;'>تم تشغيل تنظيف المسودات</p>";
}

echo "<p><a href='?cleanup=1'>تشغيل تنظيف المسودات القديمة</a></p>";

echo "<h2>الإعدادات الحالية</h2>";
echo "<p><strong>حفظ الطلبات المتروكة:</strong> " . (get_option('pexlat_form_save_abandoned_orders', 1) ? 'مفعل' : 'معطل') . "</p>";
echo "<p><strong>وضع الحفظ:</strong> " . get_option('pexlat_form_abandoned_order_save_mode', 'smart') . "</p>";
echo "<p><strong>تأخير الحفظ:</strong> " . get_option('pexlat_form_abandoned_order_delay', 30) . " ثانية</p>";
echo "<p><strong>مدة الاحتفاظ:</strong> " . get_option('pexlat_form_abandoned_order_cleanup_hours', 24) . " ساعة</p>";

?>
