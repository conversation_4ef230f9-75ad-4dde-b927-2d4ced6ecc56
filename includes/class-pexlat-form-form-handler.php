<?php
/**
 * Form handler class.
 *
 * @link       https://pexlat.com
 * @since      1.0.0
 *
 * @package    Pexlat_Form
 */

/**
 * Form handler class.
 *
 * This class handles form submissions and rendering.
 *
 * @since      1.0.0
 * @package    Pexlat_Form
 */
class Pexlat_Form_Form_Handler {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        // Register AJAX handlers for form submission
        add_action('wp_ajax_pexlat_form_submit', array($this, 'handle_form_submission'));
        add_action('wp_ajax_nopriv_pexlat_form_submit', array($this, 'handle_form_submission'));



        // إضافة معالج AJAX لحفظ الطلب كمسودة
        add_action('wp_ajax_pexlat_form_save_draft', array($this, 'handle_save_draft'));
        add_action('wp_ajax_nopriv_pexlat_form_save_draft', array($this, 'handle_save_draft'));

        // Register shortcodes
        add_shortcode('pexlat_form', array($this, 'render_form_shortcode'));
    }

    /**
     * Handle form submissions.
     *
     * @since    1.0.0
     */
    public function handle_form_submission() {
        // تحسين التحقق من الأمان مع معالجة أفضل للأخطاء
        if (!$this->verify_security_token()) {
            wp_send_json_error($this->get_security_error_message());
            exit;
        }

        // التحقق من العميل المحظور
        $phone_number = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
        $ip_address = $_SERVER['REMOTE_ADDR'];
        $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';

        $blocked_customer = $this->check_blocked_customer($phone_number, $ip_address, $email);

        if ($blocked_customer) {
            $reason = !empty($blocked_customer['reason']) ? $blocked_customer['reason'] : 'تم حظر هذا العميل من تقديم الطلبات.';
            $error_message = '<div class="blocked-message">⛔ ' . esc_html($reason) . '</div>';
            wp_send_json_error($error_message);
            exit;
        }

        // التحقق من تقييد الطلبات خلال 24 ساعة
        $order_limit_check = $this->check_order_limit($ip_address);
        if ($order_limit_check !== true) {
            wp_send_json_error($order_limit_check);
            exit;
        }

        // Get form ID
        $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;

        if ($form_id <= 0) {
            wp_send_json_error('Invalid form ID.');
            exit;
        }

        // Get form from database
        global $wpdb;
        $table_name = $wpdb->prefix . 'pexlat_form_forms';
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d AND status = 'active'", $form_id));

        if (!$form) {
            wp_send_json_error('Form not found or inactive.');
            exit;
        }

        // Get form fields
        $form_fields = Pexlat_Form_Helper::unserialize_data($form->fields);

        // Validate required fields
        $form_data = array('form_id' => $form_id);
        foreach ($form_fields as $field) {
            $field_id = $field['id'];

            // Skip if field is not visible
            if (!$field['visible']) {
                continue;
            }

            // Check if required field is missing
            if ($field['required'] && (!isset($_POST[$field_id]) || empty($_POST[$field_id]))) {
                wp_send_json_error(sprintf('الحقل "%s" مطلوب.', $field['label']));
                exit;
            }

            // Get field value
            $field_value = isset($_POST[$field_id]) ? $this->sanitize_field_value($_POST[$field_id], $field['type']) : '';

            // Store field value
            $form_data[$field_id] = array(
                'label' => $field['label'],
                'value' => $field_value,
                'type' => $field['type'],
            );
        }

        // Get redirect URL if provided
        $redirect_url = isset($_POST['redirect_url']) ? esc_url_raw($_POST['redirect_url']) : '';

        // Send notification email
        $this->send_notification($form_data, $form);

        // Return success response
        if (!empty($redirect_url)) {
            wp_send_json_success(array(
                'message' => 'تم تقديم النموذج بنجاح!',
                'redirect' => $redirect_url,
            ));
        } else {
            wp_send_json_success('تم تقديم النموذج بنجاح!');
        }
        exit;
    }

    /**
     * التحقق من العميل المحظور
     *
     * @param string $phone_number رقم الهاتف
     * @param string $ip_address عنوان IP
     * @param string $email البريد الإلكتروني
     * @return array|false مصفوفة تحتوي على بيانات العميل المحظور أو false إذا لم يكن محظوراً
     */
    private function check_blocked_customer($phone_number = '', $ip_address = '', $email = '') {
        global $wpdb;
        $blocked_table = $wpdb->prefix . 'pexlat_form_blocked_customers';

        // التأكد من وجود معيار واحد على الأقل للبحث
        if (empty($phone_number) && empty($ip_address) && empty($email)) {
            return false;
        }

        // بناء استعلامات منفصلة للبحث عن كل معيار
        $queries = array();
        $all_params = array();

        // البحث عن رقم الهاتف إذا كان متوفرًا
        if (!empty($phone_number)) {
            $phone_query = "SELECT * FROM {$blocked_table} WHERE phone_number = %s AND status = %s LIMIT 1";
            $phone_params = array($phone_number, 'active');
            $queries[] = array(
                'query' => $phone_query,
                'params' => $phone_params
            );
        }

        // البحث عن عنوان IP إذا كان متوفرًا
        if (!empty($ip_address)) {
            $ip_query = "SELECT * FROM {$blocked_table} WHERE ip_address = %s AND status = %s LIMIT 1";
            $ip_params = array($ip_address, 'active');
            $queries[] = array(
                'query' => $ip_query,
                'params' => $ip_params
            );
        }

        // البحث عن البريد الإلكتروني إذا كان متوفرًا
        if (!empty($email)) {
            $email_query = "SELECT * FROM {$blocked_table} WHERE email = %s AND status = %s LIMIT 1";
            $email_params = array($email, 'active');
            $queries[] = array(
                'query' => $email_query,
                'params' => $email_params
            );
        }

        // تنفيذ الاستعلامات واحدًا تلو الآخر
        foreach ($queries as $query_data) {
            $prepared_query = $wpdb->prepare($query_data['query'], $query_data['params']);
            $blocked_customer = $wpdb->get_row($prepared_query, ARRAY_A);

            // إذا وجدنا عميلًا محظورًا، نعيده فورًا
            if ($blocked_customer) {
                return $blocked_customer;
            }
        }

        // لم يتم العثور على عميل محظور
        return false;
    }

    /**
     * التحقق من تقييد الطلبات خلال الفترة المحددة
     *
     * @param string $ip_address عنوان IP للمستخدم
     * @return bool|string true إذا كان مسموح بالطلب، أو رسالة خطأ إذا كان هناك تقييد
     */
    private function check_order_limit($ip_address = '') {
        // التحقق من تفعيل خاصية تقييد الطلبات
        $limit_orders_enabled = get_option('pexlat_form_limit_orders_enabled', 1);

        if (!$limit_orders_enabled) {
            return true; // الخاصية غير مفعلة، السماح بالطلب
        }

        // الحصول على إعدادات تقييد الطلبات
        $max_orders = intval(get_option('pexlat_form_max_orders', 3));
        $time_period = intval(get_option('pexlat_form_time_period', 24));
        $time_period_seconds = $time_period * HOUR_IN_SECONDS;

        // الحصول على سجل الطلبات السابقة
        $orders_history = get_transient('orders_history_' . $ip_address);

        if (!$orders_history || !is_array($orders_history)) {
            // لا يوجد سجل سابق، إنشاء سجل جديد
            $orders_history = array(time());
            set_transient('orders_history_' . $ip_address, $orders_history, $time_period_seconds);
            return true;
        }

        // تنظيف السجل من الطلبات القديمة (أكثر من الفترة المحددة)
        $current_time = time();
        $cutoff_time = $current_time - $time_period_seconds;
        $recent_orders = array();

        foreach ($orders_history as $order_time) {
            if ($order_time > $cutoff_time) {
                $recent_orders[] = $order_time;
            }
        }

        // التحقق من عدد الطلبات خلال الفترة المحددة
        if (count($recent_orders) < $max_orders) {
            // إضافة الطلب الحالي إلى السجل
            $recent_orders[] = $current_time;
            set_transient('orders_history_' . $ip_address, $recent_orders, $time_period_seconds);
            return true;
        }

        // حساب الوقت المتبقي حتى يمكن إرسال طلب جديد
        sort($recent_orders); // ترتيب الطلبات من الأقدم للأحدث
        $oldest_order = $recent_orders[0]; // أقدم طلب في الفترة المحددة
        $time_until_available = ($oldest_order + $time_period_seconds) - $current_time;

        // تنسيق الوقت المتبقي بشكل مقروء
        $time_remaining_formatted = human_time_diff($current_time, $current_time + $time_until_available);

        // إنشاء رسالة الخطأ مع الترجمة
        $base_message = 'عذراً، يمكنك إرسال طلب جديد بعد';
        if (function_exists('pexlat_form_translate_text')) {
            $base_message = pexlat_form_translate_text($base_message);
        }

        if ($max_orders == 1) {
            $error_message = '<div class="blocked-message">⛔ ' . $base_message . ' ' . $time_remaining_formatted . '</div>';
        } else {
            $max_orders_message = 'عذراً، لقد وصلت للحد الأقصى من الطلبات المسموح بها';
            if (function_exists('pexlat_form_translate_text')) {
                $max_orders_message = pexlat_form_translate_text($max_orders_message);
            }
            $error_message = '<div class="blocked-message">⛔ ' . $max_orders_message . ' (' . $max_orders . '). ' . $base_message . ' ' . $time_remaining_formatted . '</div>';
        }

        return $error_message;
    }

    /**
     * Handle add to cart with form data.
     *
     * @since    1.0.0
     */
    public function handle_add_to_cart() {
        // التحقق من وجود البيانات المطلوبة
        if (empty($_POST)) {
            wp_send_json_error('لم يتم استلام أي بيانات. يرجى المحاولة مرة أخرى.');
            exit;
        }

        // تحسين التحقق من الأمان مع معالجة أفضل للأخطاء
        if (!$this->verify_security_token()) {
            wp_send_json_error($this->get_security_error_message());
            exit;
        }

        // التحقق من العميل المحظور
        $phone_number = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
        $ip_address = $_SERVER['REMOTE_ADDR'];
        $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';

        $blocked_customer = $this->check_blocked_customer($phone_number, $ip_address, $email);

        if ($blocked_customer) {
            $reason = !empty($blocked_customer['reason']) ? $blocked_customer['reason'] : 'تم حظر هذا العميل من تقديم الطلبات.';
            $error_message = '<div class="blocked-message">⛔ ' . esc_html($reason) . '</div>';
            wp_send_json_error($error_message);
            exit;
        }

        // التحقق من تقييد الطلبات خلال 24 ساعة
        $order_limit_check = $this->check_order_limit($ip_address);
        if ($order_limit_check !== true) {
            wp_send_json_error($order_limit_check);
            exit;
        }

        // Get product ID from current page if it's a product page
        global $post;
        $product_id = 0;

        // First check if product_id is directly passed in form
        if (isset($_POST['product_id']) && !empty($_POST['product_id'])) {
            $product_id = intval($_POST['product_id']);
        }
        // Otherwise try to get from global post (current product page)
        elseif (is_product() && $post && isset($post->ID)) {
            $product_id = $post->ID;
        }
        // If we're in a WooCommerce product page but global $post is not available
        elseif (function_exists('wc_get_product') && is_product()) {
            $product = wc_get_product();
            if ($product) {
                $product_id = $product->get_id();
            }
        }

        // Make sure we have a valid product ID
        if ($product_id <= 0) {
            // Try to get from referring URL
            $referer = wp_get_referer();
            if ($referer && function_exists('url_to_postid')) {
                $post_id = url_to_postid($referer);
                if ($post_id > 0 && 'product' === get_post_type($post_id)) {
                    $product_id = $post_id;
                }
            }

            // If still no product ID, check if form data has it
            if ($product_id <= 0 && isset($_POST['form_data']) && !empty($_POST['form_data']['product_id'])) {
                $product_id = intval($_POST['form_data']['product_id']);
            }

            // If still no valid product ID, check for product ID in hidden fields
            if ($product_id <= 0) {
                foreach ($_POST as $key => $value) {
                    if (strpos($key, 'product_id') !== false) {
                        $product_id = intval($value);
                        break;
                    }
                }
            }
        }

        // Final check for valid product ID
        if ($product_id <= 0) {
            wp_send_json_error('لم يتم العثور على معرف المنتج. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
            exit;
        }

        // Make sure the product exists
        $product = wc_get_product($product_id);
        if (!$product) {
            wp_send_json_error('المنتج غير متوفر.');
            exit;
        }

        // التحقق مما إذا كان النموذج معطلاً لهذا المنتج
        $disable_form = get_post_meta($product_id, '_pexlat_form_disable_form', true);
        if ($disable_form === 'yes') {
            wp_send_json_error('تم تعطيل نموذج الطلب لهذا المنتج. يرجى استخدام نموذج ووكومرس الافتراضي.');
            exit;
        }

        // Get form ID from POST data
        $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;

        // If no form ID in POST, try to get from product meta or default
        if ($form_id <= 0) {
            $product_form_id = get_post_meta($product_id, '_pexlat_form_form_id', true);
            $default_form_id = get_option('pexlat_form_default_form_id', 0);
            $form_id = !empty($product_form_id) ? $product_form_id : $default_form_id;
        }

        // Get form from database
        global $wpdb;
        $table_name = $wpdb->prefix . 'pexlat_form_forms';
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d AND status = 'active'", $form_id));

        if (!$form) {
            wp_send_json_error('النموذج غير موجود أو غير مفعّل.');
            exit;
        }

        // Get form fields
        $form_fields = Pexlat_Form_Helper::unserialize_data($form->fields);

        // Validate required fields
        $form_data = array('form_id' => $form_id);
        foreach ($form_fields as $field) {
            $field_id = $field['id'];

            // Skip if field is not visible
            if (!$field['visible']) {
                continue;
            }

            // Check if required field is missing
            if ($field['required'] && (!isset($_POST[$field_id]) || empty($_POST[$field_id]))) {
                wp_send_json_error(sprintf('الحقل "%s" مطلوب.', $field['label']));
                exit;
            }

            // Get field value
            $field_value = isset($_POST[$field_id]) ? $this->sanitize_field_value($_POST[$field_id], $field['type']) : '';

            // Store field value
            $form_data[$field_id] = array(
                'label' => $field['label'],
                'value' => $field_value,
                'type' => $field['type'],
            );
        }

        // Add product ID to form data for tracking
        $form_data['product_id'] = $product_id;

        // Get quantity if provided
        $quantity = isset($_POST['quantity']) ? max(1, intval($_POST['quantity'])) : 1;

        // Get product price
        $price = $product->get_price();
        if (isset($_POST['product_price']) && !empty($_POST['product_price'])) {
            $price = floatval($_POST['product_price']);
        }

        // Get shipping method if provided
        $shipping_method = isset($_POST['shipping_method']) ? sanitize_text_field($_POST['shipping_method']) : '';
        $shipping_cost = isset($_POST['shipping_cost']) ? floatval($_POST['shipping_cost']) : 0;

        // الحصول على معلومات المتغير إذا كان موجوداً
        $variation_id = isset($_POST['variation_id']) ? intval($_POST['variation_id']) : 0;

        // معالجة الاسم الكامل
        $full_name = isset($_POST['full_name']) ? sanitize_text_field($_POST['full_name']) : '';
        if (empty($full_name) && isset($form_data['full_name']) && isset($form_data['full_name']['value'])) {
            $full_name = $form_data['full_name']['value'];
        }

        // تقسيم الاسم الكامل إلى اسم أول واسم ثاني
        $name_parts = explode(' ', trim($full_name), 2);
        $first_name = isset($name_parts[0]) ? trim($name_parts[0]) : '';
        $last_name = isset($name_parts[1]) ? trim($name_parts[1]) : '';

        // Get phone number
        $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
        if (empty($phone) && isset($form_data['phone']) && isset($form_data['phone']['value'])) {
            $phone = $form_data['phone']['value'];
        }

        // Get address details
        $address = isset($_POST['address']) ? sanitize_text_field($_POST['address']) : '';
        if (empty($address) && isset($form_data['address']) && isset($form_data['address']['value'])) {
            $address = $form_data['address']['value'];
        }

        // Get state (province) and municipality
        $state = isset($_POST['state']) ? sanitize_text_field($_POST['state']) : '';
        if (empty($state) && isset($form_data['state']) && isset($form_data['state']['value'])) {
            $state = $form_data['state']['value'];
        }

        // الحصول على اسم الولاية من رقمها
        $state_name = '';
        if (!empty($state)) {
            // تضمين ملف بيانات الولايات
            require_once plugin_dir_path(dirname(__FILE__)) . 'includes/algeria-cities.php';
            $state_only_name = get_state_name($state);
            // عرض الرقم مع الاسم (مثل: 19 سطيف)
            $state_name = $state . ' ' . $state_only_name;
        }

        $municipality = isset($_POST['municipality']) ? sanitize_text_field($_POST['municipality']) : '';
        if (empty($municipality) && isset($form_data['municipality']) && isset($form_data['municipality']['value'])) {
            $municipality = $form_data['municipality']['value'];
        }

        // البحث عن طلب موجود مسبقاً (مسودة أو معلق) قبل إنشاء طلب جديد
        $order = null;
        $existing_order_id = null;

        // 1. البحث عن طلبات معلقة أو في الانتظار بنفس رقم الهاتف
        if (!empty($phone)) {
            // البحث عن طلبات بناءً على رقم الهاتف
            global $wpdb;



            // البحث في بيانات التعريف المخزنة في جدول البيانات الوصفية للطلب
            $pending_orders = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT post_id FROM {$wpdb->postmeta}
                     WHERE meta_key = '_billing_phone'
                     AND meta_value = %s
                     AND post_id IN (
                        SELECT ID FROM {$wpdb->posts}
                        WHERE post_type = 'shop_order'
                        AND post_status IN ('wc-pending', 'wc-on-hold')
                     )
                     ORDER BY post_id DESC
                     LIMIT 1",
                    $phone
                ),
                ARRAY_A
            );

            if (!empty($pending_orders) && isset($pending_orders[0]['post_id'])) {
                $existing_order_id = $pending_orders[0]['post_id'];
            }
            // إذا لم نجد طلبات معلقة، نتحقق من وجود مسودات طلبات
            else {
                $draft_key = 'pexlat_form_draft_' . md5($phone);
                $existing_draft = get_option($draft_key);

                if ($existing_draft && isset($existing_draft['order_id'])) {
                    $existing_order_id = $existing_draft['order_id'];
                }
            }
        }

        // 2. ثم التحقق بناءً على معرف الجلسة إذا كان موجوداً ولم نجد طلباً بعد
        if (!$existing_order_id && isset($_COOKIE['pexlat_form_session'])) {
            $session_id = sanitize_text_field($_COOKIE['pexlat_form_session']);
            $draft_key = 'pexlat_form_draft_' . $session_id;

            $existing_draft = get_option($draft_key);
            if ($existing_draft && isset($existing_draft['order_id'])) {
                $existing_order_id = $existing_draft['order_id'];
            }
        }

        // 3. محاولة الحصول على الطلب الموجود إذا وجد
        if ($existing_order_id) {
            $existing_order = wc_get_order($existing_order_id);

            // التحقق من أن الطلب موجود وأنه في حالة مسودة أو معلق
            if ($existing_order && in_array($existing_order->get_status(), array('draft', 'pending', 'on-hold'))) {
                $order = $existing_order;

                // إزالة جميع البيانات السابقة من الطلب (المنتجات ورسوم الشحن)
                $items = $order->get_items();
                foreach ($items as $item_id => $item) {
                    $order->remove_item($item_id);
                }

                $shipping_items = $order->get_items('shipping');
                foreach ($shipping_items as $shipping_item_id => $shipping_item) {
                    $order->remove_item($shipping_item_id);
                }
            }
        }

        // إذا لم نجد طلب مسودة، ننشئ طلباً جديداً
        if (!$order) {
            // إنشاء طلب جديد بحالة مسودة بدلاً من انتظار الدفع
            $order = wc_create_order(array('status' => 'draft'));

            if (!$order) {
                wp_send_json_error('فشل في إنشاء الطلب. يرجى المحاولة مرة أخرى.');
                exit;
            }
        }

        // Add product to order with variations if any
        $variations = array();
        $variation_id = 0;

        // التحقق من وجود معرف المتغير
        if (isset($_POST['variation_id']) && intval($_POST['variation_id']) > 0) {
            $variation_id = intval($_POST['variation_id']);
        }

        // جمع سمات المتغير
        foreach ($_POST as $key => $value) {
            if (strpos($key, 'attribute_') !== false) {
                $variations[$key] = sanitize_text_field($value);
            }
        }

        // حل جذري للمشكلة: استخدام طريقة مختلفة تماماً لإضافة المنتجات مع الخصم

        // 1. تحديد السعر الأصلي للمنتج أو المتغير
        $original_price = $price;
        $product_to_add = $product;

        // إذا كان هناك متغير، نستخدم سعر المتغير
        if ($variation_id > 0) {
            $variation_product = wc_get_product($variation_id);

            if ($variation_product && $variation_product->is_type('variation')) {
                // استخدام سعر المتغير
                if ($variation_original_price > 0) {
                    $original_price = $variation_original_price;
                } else {
                    $original_price = $variation_product->get_price();
                }
                $product_to_add = $variation_product;
            }
        }

        // تم إزالة قسم حساب السعر المخفض للعروض
        $discounted_unit_price = $original_price;
        $has_discount = false;

        // 3. إضافة المنتج إلى الطلب بطريقة مختلفة
        // بدلاً من استخدام add_product، سنستخدم add_item مباشرة

        // إنشاء عنصر منتج جديد
        $item = new WC_Order_Item_Product();

        // تعيين المنتج والكمية
        $item->set_product($product_to_add);
        $item->set_quantity($quantity);

        // تعيين البيانات الإضافية
        if (!empty($variations)) {
            foreach ($variations as $key => $value) {
                $item->add_meta_data(str_replace('attribute_', '', $key), $value);
            }
        }

        // تعيين السعر الأصلي والسعر المخفض
        if ($has_discount) {
            // تعيين السعر الأصلي كسعر عادي
            $item->set_subtotal($original_price * $quantity);

            // تعيين السعر المخفض كسعر نهائي
            $item->set_total($discounted_unit_price * $quantity);

            // إضافة بيانات تعريف للخصم
            $item->add_meta_data('_has_discount', 'yes');
            $item->add_meta_data('_discount_amount', $offer_discount);
            $item->add_meta_data('_discount_title', $selected_offer_title);
            $item->add_meta_data('_original_price', $original_price);
            $item->add_meta_data('_discounted_price', $discounted_unit_price);

        } else {
            // تعيين نفس السعر للسعر العادي والنهائي
            $item->set_subtotal($original_price * $quantity);
            $item->set_total($original_price * $quantity);

        }

        // إضافة العنصر إلى الطلب
        $order->add_item($item);

        // الحصول على اسم البلدية الكامل
        $municipality_name = '';

        // أولاً نحاول الحصول على اسم البلدية من حقل النص المخفي
        if (isset($_POST['municipality_text']) && !empty($_POST['municipality_text'])) {
            $municipality_name = sanitize_text_field($_POST['municipality_text']);
        }

        // إذا كان لا يزال فارغاً، نستخدم وظيفة الاسترجاع
        if (empty($municipality_name)) {
            $municipality_name = Pexlat_Form_Helper::get_municipality_name($municipality, $state);
        }

        // إعداد عناوين الدفع والشحن مع استخدام اسم الولاية
        $billing_data = array(
            'first_name' => $first_name,
            'last_name'  => $last_name,
            'phone'      => $phone,
            'country'    => 'DZ', // يمكن تغييرها حسب إعدادات الإضافة
            'state'      => $state_name, // استخدام اسم الولاية بدلاً من رقمها
            'city'       => $municipality_name, // استخدام الاسم الكامل للبلدية
            'address_1'  => $address
        );

        $order->set_address($billing_data, 'billing');
        $order->set_address($billing_data, 'shipping');

        // إضافة رسوم الشحن إذا كانت متوفرة
        if ($shipping_cost > 0) {
            $item = new WC_Order_Item_Shipping();

            // التحقق من قيمة وصحة اسم طريقة الشحن
            $method_title = 'رسوم التوصيل'; // القيمة الافتراضية

            if (!empty($shipping_method) && $shipping_method !== 'undefined') {
                // إذا كان هناك اسم لطريقة الشحن وكان مختلفًا عن "undefined"
                $method_title = $shipping_method;
            } else {
                // البحث عن اسم طريقة الشحن في بيانات النموذج
                if (isset($_POST['shipping_method_name']) && !empty($_POST['shipping_method_name'])) {
                    $method_title = sanitize_text_field($_POST['shipping_method_name']);
                }
            }

            $item->set_method_title($method_title);
            $item->set_total($shipping_cost);
            $order->add_item($item);
        }

        // تنظيف بيانات النموذج من الحقول التقنية قبل الحفظ
        $cleaned_form_data = $this->clean_form_data_for_storage($form_data);

        // حفظ بيانات النموذج في meta_data الخاصة بالطلب
        $order->update_meta_data('_pexlat_form_data', $cleaned_form_data);

        // حفظ بيانات العميل في meta_data مع استخدام اسم الولاية
        $order_meta = array(
            '_billing_first_name'  => $first_name,
            '_billing_last_name'   => $last_name,
            '_billing_phone'       => $phone,
            '_billing_country'     => 'DZ',
            '_billing_state'       => $state_name, // استخدام اسم الولاية بدلاً من رقمها
            '_billing_city'        => $municipality_name, // استخدام الاسم الكامل للبلدية
            '_billing_address_1'   => $address,
            '_shipping_first_name' => $first_name,
            '_shipping_last_name'  => $last_name,
            '_shipping_country'    => 'DZ',
            '_shipping_state'      => $state_name, // استخدام اسم الولاية بدلاً من رقمها
            '_shipping_city'       => $municipality_name, // استخدام الاسم الكامل للبلدية
            '_shipping_address_1'  => $address
        );

        foreach ($order_meta as $key => $value) {
            $order->update_meta_data($key, $value);
        }

        // حفظ بيانات إضافية مع الاسم المقسم واسم الولاية
        $order->update_meta_data('_customer_full_name', $full_name);
        $order->update_meta_data('_customer_first_name', $first_name);
        $order->update_meta_data('_customer_last_name', $last_name);
        $order->update_meta_data('_state_name', $state_name);
        $order->update_meta_data('_state_code', $state);

        // حفظ طريقة الدفع المختارة
        if (isset($_POST['payment_method']) && !empty($_POST['payment_method'])) {
            $payment_method = sanitize_text_field($_POST['payment_method']);

            // التحقق من أن طريقة الدفع متاحة في WooCommerce
            if (class_exists('WooCommerce')) {
                $available_gateways = WC()->payment_gateways->get_available_payment_gateways();
                if (isset($available_gateways[$payment_method])) {
                    $gateway = $available_gateways[$payment_method];

                    // حفظ طريقة الدفع في الطلب بالطريقة الصحيحة
                    $order->set_payment_method($payment_method);
                    $order->set_payment_method_title($gateway->get_title());

                    // حفظ معلومات إضافية عن طريقة الدفع
                    $order->update_meta_data('_pexlat_form_payment_method', $payment_method);
                    $order->update_meta_data('_pexlat_form_payment_method_title', $gateway->get_title());

                    // حفظ طريقة الدفع في الجلسة أيضاً لضمان التحديد التلقائي
                    if (WC()->session) {
                        WC()->session->set('chosen_payment_method', $payment_method);
                    }

                    // إذا كانت طريقة الدفع تحتاج معالجة إضافية
                    if ($gateway->has_fields()) {
                        $order->update_meta_data('_pexlat_form_payment_needs_processing', 'yes');
                        $order->add_order_note('طريقة الدفع المختارة تحتاج معالجة إضافية: ' . $gateway->get_title());
                    }
                }
            }
        }

        // تم إزالة قسم العروض والخصومات

        // حفظ وقت الطلب لتقييد الطلبات
        $spam_settings = array(
            'limit_orders' => get_option('pexlat_form_limit_orders_enabled', 1),
            'max_orders' => intval(get_option('pexlat_form_max_orders', 3)),
            'time_period' => intval(get_option('pexlat_form_time_period', 24))
        );

        if ($spam_settings['limit_orders']) {
            $user_ip = $_SERVER['REMOTE_ADDR'];
            $time_period_seconds = $spam_settings['time_period'] * HOUR_IN_SECONDS;

            // الحصول على سجل الطلبات السابقة
            $orders_history = get_transient('orders_history_' . $user_ip);

            if (!$orders_history || !is_array($orders_history)) {
                // إنشاء سجل جديد
                $orders_history = array(time());
            } else {
                // إضافة الطلب الحالي إلى السجل
                $orders_history[] = time();

                // تنظيف السجل من الطلبات القديمة
                $current_time = time();
                $cutoff_time = $current_time - $time_period_seconds;
                $recent_orders = array();

                foreach ($orders_history as $order_time) {
                    if ($order_time > $cutoff_time) {
                        $recent_orders[] = $order_time;
                    }
                }

                $orders_history = $recent_orders;
            }

            // حفظ السجل المحدث
            set_transient('orders_history_' . $user_ip, $orders_history, $time_period_seconds);
        }

        // التحقق من تفعيل طرق الدفع لتحديد حالة الطلب
        $form_settings = Pexlat_Form_Helper::unserialize_data($form->settings);
        $payment_methods_enabled = isset($form_settings['payment_methods_enabled']) && $form_settings['payment_methods_enabled'] == 1;
        $selected_payment_method = isset($_POST['payment_method']) ? sanitize_text_field($_POST['payment_method']) : '';

        // تحديد ما إذا كانت طريقة الدفع تحتاج معالجة فورية
        $needs_payment_processing = false;
        $gateway_title = '';

        if ($payment_methods_enabled && !empty($selected_payment_method)) {
            // التحقق من طريقة الدفع المختارة
            if (class_exists('WooCommerce')) {
                $available_gateways = WC()->payment_gateways->get_available_payment_gateways();
                if (isset($available_gateways[$selected_payment_method])) {
                    $gateway = $available_gateways[$selected_payment_method];
                    $gateway_title = $gateway->get_title();

                    // طرق الدفع التي لا تحتاج معالجة فورية
                    $direct_payment_methods = array('cod', 'bacs'); // الدفع عند الاستلام والتحويل البنكي

                    if (!in_array($selected_payment_method, $direct_payment_methods)) {
                        $needs_payment_processing = true;
                    }
                }
            }
        }

        // تعيين حالة الطلب حسب طريقة الدفع
        if ($needs_payment_processing) {
            // إذا كانت طريقة الدفع تحتاج معالجة، نضع الطلب في انتظار الدفع
            $order->set_status('pending');
        } else {
            // إذا كان الدفع عند الاستلام أو لا توجد طرق دفع مفعلة، نضع الطلب قيد المعالجة
            $order->set_status('processing');
        }
        // حساب إجمالي الطلب وحفظه
        try {
            $order->calculate_totals();

        } catch (Exception $e) {
            wp_send_json_error('حدث خطأ أثناء حساب إجمالي الطلب: ' . $e->getMessage());
        }

        try {
            $order->save();

            // تحديد نوع الاستجابة حسب حالة الطلب
            if ($needs_payment_processing) {
                // التأكد من أن الطلب في الحالة الصحيحة للدفع
                if ($order->get_status() !== 'pending') {
                    $order->set_status('pending');
                    $order->save();
                }

                // التحقق من إعداد تخطي صفحة الدفع
                $skip_checkout = isset($form_settings['skip_checkout_page']) && $form_settings['skip_checkout_page'] == 1;

                if ($skip_checkout) {
                    // توجيه مباشر لصفحة الدفع الخاصة بطريقة الدفع
                    $checkout_url = $gateway->get_return_url($order);

                    // إذا لم تنجح الطريقة الأولى، استخدم طريقة بديلة
                    if (empty($checkout_url) || $checkout_url === home_url('/')) {
                        // محاولة الحصول على رابط الدفع المباشر
                        if (method_exists($gateway, 'process_payment')) {
                            // تجهيز البيانات للدفع
                            $payment_result = $gateway->process_payment($order->get_id());
                            if (isset($payment_result['redirect'])) {
                                $checkout_url = $payment_result['redirect'];
                            }
                        }
                    }

                    // إذا لم تنجح الطرق السابقة، استخدم رابط الدفع العادي
                    if (empty($checkout_url) || $checkout_url === home_url('/')) {
                        $checkout_url = $order->get_checkout_payment_url();
                    }
                } else {
                    // إذا كان الطلب يحتاج دفع، نوجه المستخدم إلى صفحة الدفع المحددة للطلب
                    $checkout_url = $order->get_checkout_payment_url();
                }

                // إضافة معاملات إضافية لضمان تحديد طريقة الدفع
                $checkout_url = add_query_arg(array(
                    'payment_method' => $selected_payment_method,
                    'pexlat_form_order' => '1'
                ), $checkout_url);

                // التأكد من أن الرابط صحيح
                if (empty($checkout_url) || strpos($checkout_url, 'order-pay') === false) {
                    // إذا فشل الحصول على رابط الدفع، استخدم الطريقة البديلة
                    $checkout_url = add_query_arg(array(
                        'order-pay' => $order->get_id(),
                        'pay_for_order' => 'true',
                        'key' => $order->get_order_key(),
                        'payment_method' => $selected_payment_method,
                        'pexlat_form_order' => '1'
                    ), wc_get_checkout_url());
                }

                // طريقة بديلة أخرى إذا لم تنجح الأولى
                if (empty($checkout_url)) {
                    $checkout_url = home_url('/checkout/order-pay/' . $order->get_id() . '/?pay_for_order=true&key=' . $order->get_order_key() . '&payment_method=' . $selected_payment_method . '&pexlat_form_order=1');
                }

                $success_message = sprintf('تم إنشاء طلبك بنجاح. سيتم توجيهك لإكمال الدفع عبر %s.', $gateway_title);
                if (function_exists('pexlat_form_translate_text')) {
                    $success_message = pexlat_form_translate_text($success_message);
                }

                // إضافة ملاحظة للطلب مع معلومات تشخيصية
                $order->add_order_note(sprintf(
                    'العميل اختار طريقة الدفع: %s. في انتظار إكمال الدفع. رابط الدفع: %s. حالة الطلب: %s. عدد المنتجات: %d. إجمالي الطلب: %s',
                    $gateway_title,
                    $checkout_url,
                    $order->get_status(),
                    $order->get_item_count(),
                    $order->get_total()
                ));

                wp_send_json_success(array(
                    'message' => $success_message,
                    'order_id' => $order->get_id(),
                    'redirect' => $checkout_url,
                    'needs_payment' => true,
                    'payment_method' => $selected_payment_method,
                    'payment_method_title' => $gateway_title,
                    'order_status' => $order->get_status(),
                    'order_total' => $order->get_total(),
                    'item_count' => $order->get_item_count(),
                    'debug_info' => array(
                        'order_key' => $order->get_order_key(),
                        'checkout_url_method' => 'get_checkout_payment_url'
                    )
                ));
            } else {
                // السلوك العادي للدفع عند الاستلام أو عدم تفعيل طرق الدفع
                $this->send_notification($form_data, $order);

                $redirect_url = $this->get_custom_redirect_url($form_id, $order);

                if ($selected_payment_method === 'cod') {
                    $success_message = 'تم إرسال طلبك بنجاح. سيتم التواصل معك لتأكيد الطلب والتوصيل.';
                } elseif ($selected_payment_method === 'bacs') {
                    $success_message = 'تم إرسال طلبك بنجاح. سيتم التواصل معك لتزويدك بتفاصيل التحويل البنكي.';
                } else {
                    $success_message = 'تم إرسال طلبك بنجاح.';
                }

                if (function_exists('pexlat_form_translate_text')) {
                    $success_message = pexlat_form_translate_text($success_message);
                }

                wp_send_json_success(array(
                    'message' => $success_message,
                    'order_id' => $order->get_id(),
                    'redirect' => $redirect_url,
                    'needs_payment' => false,
                    'payment_method' => $selected_payment_method
                ));
            }
        } catch (Exception $e) {

            // إرسال رسالة خطأ للمستخدم
            wp_send_json_error('حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى لاحقًا أو الاتصال بالدعم الفني.');
        }
        exit;
    }

    /**
     * Render form shortcode.
     *
     * @since    1.0.0
     * @param    array    $atts    Shortcode attributes.
     * @return   string            Form HTML.
     */
    public function render_form_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => 0,
            'product_id' => 0, // إضافة معلمة معرف المنتج
        ), $atts, 'pexlat_form');

        $form_id = intval($atts['id']);
        $product_id = intval($atts['product_id']); // الحصول على معرف المنتج من الشورت كود

        if ($form_id <= 0) {
            return '<p>معرف النموذج غير صالح.</p>';
        }

        // التحقق من وجود المنتج إذا تم تحديد معرف المنتج
        if ($product_id > 0) {
            $product = wc_get_product($product_id);
            if (!$product) {
                return '<p>المنتج غير موجود أو غير متاح.</p>';
            }

            // التحقق مما إذا كان النموذج معطلاً لهذا المنتج
            $disable_form = get_post_meta($product_id, '_pexlat_form_disable_form', true);
            if ($disable_form === 'yes') {
                return '<p>تم تعطيل نموذج الطلب لهذا المنتج.</p>';
            }
        }

        // Get form from database
        global $wpdb;
        $table_name = $wpdb->prefix . 'pexlat_form_forms';
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d AND status = 'active'", $form_id));

        if (!$form) {
            return '<p>النموذج غير موجود أو غير مفعّل.</p>';
        }

        // Get form settings and fields (استخدام الطريقة الآمنة في جلب البيانات)
        $settings = Pexlat_Form_Helper::unserialize_data($form->settings);
        $fields = Pexlat_Form_Helper::unserialize_data($form->fields);

        // Prepare form data for template
        $form_data = array(
            'form_id' => $form->id,
            'settings' => array(
                'form_settings' => $settings,
                'fields' => $fields,
            ),
            'product_id' => $product_id, // استخدام معرف المنتج من الشورت كود
            'is_shortcode' => true, // إشارة أن هذا شورت كود
        );

        // Start output buffering
        ob_start();

        // إضافة حاوي خاص بالشورت كود
        echo '<div class="pexlat-form-shortcode-wrapper">';
        echo '<div class="pexlat-form-shortcode-container">';

        // Include the form template
        include plugin_dir_path(dirname(__FILE__)) . 'public/partials/form-template.php';

        echo '</div>'; // إغلاق pexlat-form-shortcode-container
        echo '</div>'; // إغلاق pexlat-form-shortcode-wrapper

        // Return the form HTML
        return ob_get_clean();
    }

    /**
     * Sanitize field value based on field type.
     *
     * @since    1.0.0
     * @param    mixed     $value    The field value.
     * @param    string    $type     The field type.
     * @return   mixed                Sanitized value.
     */
    private function sanitize_field_value($value, $type) {
        switch ($type) {
            case 'text':
                return sanitize_text_field($value);
            case 'email':
                return sanitize_email($value);
            case 'textarea':
                return sanitize_textarea_field($value);
            case 'number':
                return intval($value);
            case 'tel':
                return sanitize_text_field($value);
            case 'select':
                return sanitize_text_field($value);
            case 'checkbox':
                // For checkbox, value could be an array
                if (is_array($value)) {
                    return array_map('sanitize_text_field', $value);
                }
                return sanitize_text_field($value);
            case 'radio':
                return sanitize_text_field($value);
            default:
                return sanitize_text_field($value);
        }
    }

    /**
     * تنظيف بيانات النموذج من الحقول التقنية قبل الحفظ
     *
     * @since    1.0.0
     * @param    array    $form_data    بيانات النموذج الأصلية
     * @return   array                  بيانات النموذج المنظفة
     */
    private function clean_form_data_for_storage($form_data) {
        if (!is_array($form_data)) {
            return $form_data;
        }

        // قائمة الحقول التقنية التي يجب إزالتها من التخزين
        $technical_fields = array(
            'wp_http_referer', '_wp_http_referer', 'action', 'nonce', '_wpnonce',
            'form_id', 'product_id', 'variation_id', 'quantity'
        );

        $cleaned_data = array();

        foreach ($form_data as $field_id => $field_data) {
            // تخطي الحقول التقنية
            if (in_array($field_id, $technical_fields)) {
                continue;
            }

            // الاحتفاظ بالحقول الأخرى
            $cleaned_data[$field_id] = $field_data;
        }

        return $cleaned_data;
    }

    /**
     * Free database results to prevent "Commands out of sync" errors.
     *
     * Using the shared Helper class for this functionality.
     *
     * @since    1.0.0
     */
    private function free_results() {
        Pexlat_Form_Helper::free_results();
    }

    /**
     * Get custom redirect URL based on form settings.
     *
     * @since    1.0.0
     * @param    int      $form_id    The form ID.
     * @param    object   $order      The WooCommerce order.
     * @return   string               The redirect URL.
     */
    private function get_custom_redirect_url($form_id, $order) {
        // Get form settings
        global $wpdb;
        $table_name = $wpdb->prefix . 'pexlat_form_forms';
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d", $form_id));

        if (!$form) {
            // Default WooCommerce thank you page URL
            return $this->get_woocommerce_thankyou_url($order);
        }

        // Get form settings (استخدام الطريقة الآمنة في جلب البيانات)
        $settings = Pexlat_Form_Helper::unserialize_data($form->settings);

        // التحقق من وجود صفحة توجيه مخصصة
        if (isset($settings['redirect_page_id']) && !empty($settings['redirect_page_id'])) {
            // الحصول على رابط الصفحة المخصصة
            $page_id = intval($settings['redirect_page_id']);
            $custom_page_url = get_permalink($page_id);

            // إضافة معرف الطلب كمعلمة استعلام
            if ($custom_page_url) {
                return add_query_arg(array(
                    'order_id' => $order->get_id(),
                    'key' => $order->get_order_key()
                ), $custom_page_url);
            }
        }

        // إذا كان هناك رابط مخصص مباشر (للتوافق مع الإصدارات السابقة)
        if (isset($settings['redirect_url']) && !empty($settings['redirect_url'])) {
            $redirect_url = $settings['redirect_url'];

            // إضافة معرف الطلب كمعلمة استعلام
            return add_query_arg(array(
                'order_id' => $order->get_id(),
                'key' => $order->get_order_key()
            ), $redirect_url);
        }

        // العودة إلى صفحة شكر WooCommerce الافتراضية
        return $this->get_woocommerce_thankyou_url($order);
    }

    /**
     * Get the default WooCommerce thank you/order received URL.
     *
     * @since    1.0.0
     * @param    object   $order      The WooCommerce order.
     * @return   string               The thank you URL.
     */
    private function get_woocommerce_thankyou_url($order) {
        // Use WooCommerce's order-received endpoint URL rather than the view-order endpoint
        $order_received_url = wc_get_endpoint_url('order-received', $order->get_id(), wc_get_checkout_url());
        $order_received_url = add_query_arg('key', $order->get_order_key(), $order_received_url);

        return $order_received_url;
    }

    /**
     * معالجة حفظ الطلب كمسودة عند إدخال البيانات دون إكمال الطلب
     *
     * @since    1.0.0
     */
    public function handle_save_draft() {
        // التحقق مما إذا كان حفظ الطلبات المتروكة مفعل
        if (get_option('pexlat_form_save_abandoned_orders', 1) != 1) {
            wp_send_json_error('حفظ الطلبات المتروكة غير مفعل.');
            exit;
        }
        
        // تحسين التحقق من الأمان مع معالجة أفضل للأخطاء
        if (!$this->verify_security_token()) {
            wp_send_json_error($this->get_security_error_message());
            exit;
        }

        // التحقق من العميل المحظور
        $phone_number = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
        $ip_address = $_SERVER['REMOTE_ADDR'];
        $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';

        $blocked_customer = $this->check_blocked_customer($phone_number, $ip_address, $email);

        if ($blocked_customer) {
            $reason = !empty($blocked_customer['reason']) ? $blocked_customer['reason'] : 'تم حظر هذا العميل من تقديم الطلبات.';
            $error_message = '<div class="blocked-message">⛔ ' . esc_html($reason) . '</div>';
            wp_send_json_error($error_message);
            exit;
        }

        // لا نقوم بالتحقق من تقييد الطلبات خلال 24 ساعة هنا لأنها مجرد مسودة

        // Get form ID and product ID
        $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;
        $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;

        if ($form_id <= 0) {
            wp_send_json_error('معرف النموذج غير صالح.');
            exit;
        }

        // جمع البيانات المدخلة حتى الآن
        $form_data = array(
            'form_id' => $form_id,
            'product_id' => $product_id,
            'timestamp' => current_time('mysql'),
            'partial_data' => array()
        );

        // جمع جميع البيانات المرسلة من النموذج
        foreach ($_POST as $key => $value) {
            // نستبعد الحقول الخاصة بالنظام
            if (!in_array($key, array('action', 'nonce', 'form_id'))) {
                $form_data['partial_data'][$key] = $this->sanitize_field_value($value, 'text');
            }
        }

        // التحقق من وجود بيانات كافية للحفظ
        if (empty($form_data['partial_data'])) {
            wp_send_json_error('لا توجد بيانات لحفظها.');
            exit;
        }

        // التحقق من وجود رقم الهاتف أو الاسم على الأقل
        $has_contact_info = false;
        if (
            (isset($form_data['partial_data']['phone']) && !empty($form_data['partial_data']['phone'])) ||
            (isset($form_data['partial_data']['full_name']) && !empty($form_data['partial_data']['full_name']))
        ) {
            $has_contact_info = true;
        }

        if (!$has_contact_info) {
            // لا داعي لإرسال خطأ، فقط نسجل أنه لا توجد معلومات اتصال كافية
            wp_send_json_success(array('status' => 'no_contact_info'));
            exit;
        }

        // فحص ما إذا كان هذا طلب موجود مسبقاً
        $draft_key = null;
        $existing_draft_id = null;
        $ip_address = $_SERVER['REMOTE_ADDR'];

        // إذا كان لدينا رقم هاتف، نبحث عنه في المسودات السابقة
        if (isset($form_data['partial_data']['phone']) && !empty($form_data['partial_data']['phone'])) {
            $phone = $form_data['partial_data']['phone'];
            $draft_key = 'pexlat_form_draft_' . md5($phone . '_' . $ip_address);

            // البحث عن مسودة حالية بنفس رقم الهاتف وعنوان IP
            $existing_draft = get_option($draft_key);
            if ($existing_draft && isset($existing_draft['order_id'])) {
                $existing_draft_id = $existing_draft['order_id'];

                // التحقق من أن الطلب لا يزال في حالة مسودة وتم إنشاؤه خلال آخر 24 ساعة
                $order_check = wc_get_order($existing_draft_id);
                if (!$order_check || $order_check->get_status() !== 'draft') {
                    // الطلب لم يعد مسودة، نحذف المفتاح ونبدأ من جديد
                    delete_option($draft_key);
                    $existing_draft_id = null;
                } else {
                    // التحقق من تاريخ إنشاء الطلب
                    $order_date = $order_check->get_date_created();
                    if ($order_date && $order_date->getTimestamp() < (time() - 24 * HOUR_IN_SECONDS)) {
                        // الطلب قديم جداً، نحذفه ونبدأ من جديد
                        wp_delete_post($existing_draft_id, true);
                        delete_option($draft_key);
                        $existing_draft_id = null;
                    }
                }
            }
        } else {
            // استخدام معرف جلسة فريد لتتبع المستخدم مع عنوان IP
            $session_id = isset($_COOKIE['pexlat_form_session']) ? sanitize_text_field($_COOKIE['pexlat_form_session']) : md5(uniqid('draft_', true));
            $draft_key = 'pexlat_form_draft_' . md5($session_id . '_' . $ip_address);

            // وضع كوكي للمتصفح لتتبع هذه الجلسة
            if (!isset($_COOKIE['pexlat_form_session'])) {
                setcookie('pexlat_form_session', $session_id, time() + 30 * DAY_IN_SECONDS, COOKIEPATH, COOKIE_DOMAIN);
            }

            // البحث عن مسودة حالية بنفس معرف الجلسة وعنوان IP
            $existing_draft = get_option($draft_key);
            if ($existing_draft && isset($existing_draft['order_id'])) {
                $existing_draft_id = $existing_draft['order_id'];

                // التحقق من صحة الطلب
                $order_check = wc_get_order($existing_draft_id);
                if (!$order_check || $order_check->get_status() !== 'draft') {
                    delete_option($draft_key);
                    $existing_draft_id = null;
                }
            }
        }

        $order = null;

        // إذا وجدنا مسودة موجودة، نحاول تحديثها
        if ($existing_draft_id) {
            $order = wc_get_order($existing_draft_id);

            // التحقق من أن الطلب موجود وأنه في حالة مسودة
            if ($order && $order->get_status() == 'draft') {
            } else {
                // إذا لم يكن الطلب موجوداً أو ليس في حالة مسودة، نقوم بإنشاء طلب جديد
                $existing_draft_id = null;
                $order = null;
            }
        }

        // إذا لم يكن لدينا طلب مسودة، ننشئ واحداً جديداً
        if (!$order) {
            // إنشاء طلب جديد كمسودة
            $order = wc_create_order(array('status' => 'draft'));

            if (!$order) {
                wp_send_json_error('فشل في إنشاء طلب مسودة. يرجى المحاولة مرة أخرى.');
                exit;
            }

        }

        // إضافة المنتج إلى الطلب إذا كان معرف المنتج متوفراً
        if ($product_id > 0) {
            $product = wc_get_product($product_id);

            if ($product) {
                $quantity = isset($form_data['partial_data']['quantity']) ? max(1, intval($form_data['partial_data']['quantity'])) : 1;
                $price = $product->get_price();

                // التحقق من وجود نفس المنتج في الطلب
                $items = $order->get_items();
                $product_exists = false;

                foreach ($items as $item_id => $item) {
                    if ($item->get_product_id() == $product_id) {
                        // تحديث الكمية للمنتج الموجود
                        $item->set_quantity($quantity);
                        $item->set_subtotal($price * $quantity);
                        $item->set_total($price * $quantity);
                        $item->save();
                        $product_exists = true;
                        break;
                    }
                }

                // إذا لم يكن المنتج موجوداً، أضفه
                if (!$product_exists) {
                    // إزالة أي منتجات أخرى (للحفاظ على منتج واحد فقط)
                    foreach ($items as $item_id => $item) {
                        $order->remove_item($item_id);
                    }

                    $item_data = array(
                        'subtotal' => $price * $quantity,
                        'total' => $price * $quantity
                    );

                    $order->add_product($product, $quantity, $item_data);
                }
            }
        }

        // تحديث بيانات العميل في الطلب
        $customer_data = array();

        if (isset($form_data['partial_data']['full_name']) && !empty($form_data['partial_data']['full_name'])) {
            $customer_data['first_name'] = $form_data['partial_data']['full_name'];
            $order->update_meta_data('_customer_full_name', $form_data['partial_data']['full_name']);
        }

        if (isset($form_data['partial_data']['phone']) && !empty($form_data['partial_data']['phone'])) {
            $customer_data['phone'] = $form_data['partial_data']['phone'];
            $order->update_meta_data('_customer_phone', $form_data['partial_data']['phone']);
        }

        if (isset($form_data['partial_data']['address']) && !empty($form_data['partial_data']['address'])) {
            $customer_data['address_1'] = $form_data['partial_data']['address'];
        }

        if (isset($form_data['partial_data']['state']) && !empty($form_data['partial_data']['state'])) {
            // الحصول على اسم الولاية من رقمها
            $state_code = $form_data['partial_data']['state'];
            require_once plugin_dir_path(dirname(__FILE__)) . 'includes/algeria-cities.php';
            $state_name = get_state_name($state_code);
            $customer_data['state'] = $state_name; // استخدام اسم الولاية بدلاً من رقمها
        }

        if (isset($form_data['partial_data']['municipality']) && !empty($form_data['partial_data']['municipality'])) {
            $municipality = $form_data['partial_data']['municipality'];

            // محاولة الحصول على اسم البلدية
            $municipality_name = '';
            if (isset($form_data['partial_data']['municipality_text']) && !empty($form_data['partial_data']['municipality_text'])) {
                $municipality_name = $form_data['partial_data']['municipality_text'];
            } else {
                $state = isset($form_data['partial_data']['state']) ? $form_data['partial_data']['state'] : '';
                $municipality_name = Pexlat_Form_Helper::get_municipality_name($municipality, $state);
            }

            $customer_data['city'] = $municipality_name;
        }

        // تطبيق بيانات العميل على عناوين الدفع والشحن
        if (!empty($customer_data)) {
            // إضافة DZ كرمز الدولة الافتراضي
            $customer_data['country'] = 'DZ';

            $order->set_address($customer_data, 'billing');
            $order->set_address($customer_data, 'shipping');
        }

        // حفظ النموذج الكامل كـ meta data
        $order->update_meta_data('_pexlat_form_draft_data', $form_data);

        // حفظ المنتج المرتبط بالمسودة
        if ($product_id > 0) {
            $order->update_meta_data('_pexlat_form_product_id', $product_id);
        }

        // حفظ نموذج المرتبط بالمسودة
        $order->update_meta_data('_pexlat_form_form_id', $form_id);

        // حفظ تاريخ إنشاء المسودة أو تحديثها
        $order->update_meta_data('_pexlat_form_draft_created', current_time('mysql'));
        $order->update_meta_data('_pexlat_form_draft_updated', current_time('mysql'));

        // حفظ الطلب أولاً
        $order->save();

        // التأكد من حفظ الطلب وإعادة تحميله
        $order = wc_get_order($order->get_id());

        // التحقق من أن الطلب يحتوي على منتجات
        if ($order->get_item_count() == 0) {
            wp_send_json_error('فشل في إضافة المنتجات للطلب. يرجى المحاولة مرة أخرى.');
            exit;
        }

        // حفظ معلومات المسودة في الخيارات
        $draft_info = array(
            'order_id' => $order->get_id(),
            'form_id' => $form_id,
            'product_id' => $product_id,
            'created' => current_time('mysql'),
            'updated' => current_time('mysql'),
            'ip_address' => $ip_address,
            'phone' => isset($form_data['partial_data']['phone']) ? $form_data['partial_data']['phone'] : '',
            'last_activity' => time()
        );

        // تعيين انتهاء صلاحية المسودة بعد 24 ساعة
        update_option($draft_key, $draft_info, false);

        // إضافة مؤقت لحذف المسودات القديمة بناءً على الإعدادات
        $cleanup_hours = intval(get_option('pexlat_form_abandoned_order_cleanup_hours', 24));
        wp_schedule_single_event(time() + $cleanup_hours * HOUR_IN_SECONDS, 'pexlat_form_cleanup_old_drafts');

        // الرد بنجاح
        wp_send_json_success(array(
            'status' => 'draft_saved',
            'order_id' => $order->get_id(),
            'draft_key' => $draft_key
        ));
        exit;
    }

    /**
     * Send notification email.
     *
     * @since    1.0.0
     * @param    array    $data       The form data.
     * @param    object   $order      The WooCommerce order.
     */
    private function send_notification($data, $order) {
        $to = get_option('pexlat_form_notification_email', get_option('admin_email'));
        $subject = sprintf('طلب جديد #%s', $order->get_id());

        // الحصول على تفاصيل المنتج
        $items = $order->get_items();
        $product_details = '';
        foreach ($items as $item) {
            $product_details .= sprintf('<strong>%s</strong> - %s x %s = %s<br>',
                $item->get_name(),
                $item->get_quantity(),
                wc_price($item->get_subtotal() / $item->get_quantity()),
                wc_price($item->get_subtotal())
            );

            // إضافة التفاصيل إذا كانت متوفرة (للمتغيرات)
            $variation_data = $item->get_formatted_meta_data();
            if (!empty($variation_data)) {
                foreach ($variation_data as $meta) {
                    $product_details .= sprintf(' - %s: %s<br>', $meta->display_key, $meta->display_value);
                }
            }
        }

        // Build message
        $message = sprintf('<h2>تم استلام طلب جديد #%s</h2>', $order->get_id());

        // معلومات العميل
        $message .= '<h3>معلومات العميل:</h3>';
        $message .= '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
        $message .= sprintf('<tr><th style="padding: 8px; text-align: right; border: 1px solid #ddd; width: 30%%;">الاسم:</th><td style="padding: 8px; text-align: right; border: 1px solid #ddd;">%s</td></tr>', $order->get_billing_first_name() . ' ' . $order->get_billing_last_name());
        $message .= sprintf('<tr><th style="padding: 8px; text-align: right; border: 1px solid #ddd; width: 30%%;">الهاتف:</th><td style="padding: 8px; text-align: right; border: 1px solid #ddd;">%s</td></tr>', $order->get_billing_phone());
        $message .= sprintf('<tr><th style="padding: 8px; text-align: right; border: 1px solid #ddd; width: 30%%;">العنوان:</th><td style="padding: 8px; text-align: right; border: 1px solid #ddd;">%s</td></tr>', $order->get_billing_address_1());
        $message .= sprintf('<tr><th style="padding: 8px; text-align: right; border: 1px solid #ddd; width: 30%%;">الولاية:</th><td style="padding: 8px; text-align: right; border: 1px solid #ddd;">%s</td></tr>', $order->get_billing_state());
        $message .= sprintf('<tr><th style="padding: 8px; text-align: right; border: 1px solid #ddd; width: 30%%;">البلدية:</th><td style="padding: 8px; text-align: right; border: 1px solid #ddd;">%s</td></tr>', $order->get_billing_city());
        $message .= '</table>';

        // تفاصيل المنتج
        $message .= '<h3>تفاصيل المنتج:</h3>';
        $message .= '<div style="margin-bottom: 20px;">' . $product_details . '</div>';

        // معلومات الشحن
        $shipping_total = $order->get_shipping_total();
        if ($shipping_total > 0) {
            $message .= '<h3>معلومات الشحن:</h3>';
            $message .= '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
            foreach ($order->get_shipping_methods() as $shipping_method) {
                $message .= sprintf('<tr><th style="padding: 8px; text-align: right; border: 1px solid #ddd; width: 30%%;">طريقة الشحن:</th><td style="padding: 8px; text-align: right; border: 1px solid #ddd;">%s</td></tr>', $shipping_method->get_method_title());
                $message .= sprintf('<tr><th style="padding: 8px; text-align: right; border: 1px solid #ddd; width: 30%%;">تكلفة الشحن:</th><td style="padding: 8px; text-align: right; border: 1px solid #ddd;">%s</td></tr>', wc_price($shipping_method->get_total()));
            }
            $message .= '</table>';
        }

        // معلومات دفع إضافية من النموذج
        $message .= '<h3>معلومات إضافية:</h3>';
        $message .= '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';

        foreach ($data as $field_id => $field_data) {
            // تخطي الحقول التي ليست بيانات حقل
            if (in_array($field_id, array('form_id', 'product_id', 'action', 'nonce'))) {
                continue;
            }

            // تخطي الحقول التي تم عرضها بالفعل (الاسم، الهاتف، العنوان، الولاية، البلدية)
            if (in_array($field_id, array('full_name', 'phone', 'address', 'state', 'municipality'))) {
                continue;
            }

            $message .= '<tr>';
            $message .= sprintf('<th style="padding: 8px; text-align: right; border: 1px solid #ddd; width: 30%%;">%s:</th>', $field_data['label']);

            // Handle different value types
            if (is_array($field_data['value'])) {
                $message .= sprintf('<td style="padding: 8px; text-align: right; border: 1px solid #ddd;">%s</td>', implode(', ', $field_data['value']));
            } else {
                $message .= sprintf('<td style="padding: 8px; text-align: right; border: 1px solid #ddd;">%s</td>', $field_data['value']);
            }

            $message .= '</tr>';
        }

        $message .= '</table>';

        // معلومات العرض المطبق (إذا وجد)
        $selected_offer_title = $order->get_meta('_selected_offer_title');
        $offer_discount = $order->get_meta('_offer_discount');

        // تم إزالة قسم العروض والخصومات من الرسالة

        // الإجمالي
        $message .= '<h3>إجمالي الطلب:</h3>';
        $message .= '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
        $message .= sprintf('<tr><th style="padding: 8px; text-align: right; border: 1px solid #ddd; width: 30%%;">المجموع الفرعي:</th><td style="padding: 8px; text-align: right; border: 1px solid #ddd;">%s</td></tr>', wc_price($order->get_subtotal()));
        if ($offer_discount > 0) {
            $message .= sprintf('<tr><th style="padding: 8px; text-align: right; border: 1px solid #ddd; width: 30%%;">الخصم:</th><td style="padding: 8px; text-align: right; border: 1px solid #ddd;">- %s</td></tr>', wc_price($offer_discount));
        }
        if ($shipping_total > 0) {
            $message .= sprintf('<tr><th style="padding: 8px; text-align: right; border: 1px solid #ddd; width: 30%%;">الشحن:</th><td style="padding: 8px; text-align: right; border: 1px solid #ddd;">%s</td></tr>', wc_price($shipping_total));
        }
        $message .= sprintf('<tr><th style="padding: 8px; text-align: right; border: 1px solid #ddd; width: 30%%;">الإجمالي:</th><td style="padding: 8px; text-align: right; border: 1px solid #ddd;">%s</td></tr>', wc_price($order->get_total()));
        $message .= '</table>';

        $message .= sprintf('<p>تم استلام هذا الطلب في %s</p>', $order->get_date_created()->date_i18n(get_option('date_format') . ' ' . get_option('time_format')));
        $message .= sprintf('<p><a href="%s" style="background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">عرض الطلب في لوحة التحكم</a></p>', admin_url('post.php?post=' . $order->get_id() . '&action=edit'));

        // Set headers
        $headers = array('Content-Type: text/html; charset=UTF-8');

        // Send email
        wp_mail($to, $subject, $message, $headers);

        // إرسال بريد إلكتروني للعميل أيضًا (اختياري حسب الإعدادات)
        $send_to_customer = get_option('pexlat_form_send_customer_email', true);
        if ($send_to_customer && !empty($order->get_billing_email())) {
            $customer_subject = sprintf('تأكيد طلبك #%s', $order->get_id());
            $customer_message = sprintf('<h2>شكراً لطلبك، %s!</h2>', $order->get_billing_first_name());
            $customer_message .= '<p>تم استلام طلبك بنجاح وهو قيد المعالجة الآن.</p>';
            $customer_message .= sprintf('<h3>تفاصيل الطلب #%s:</h3>', $order->get_id());
            $customer_message .= '<div style="margin-bottom: 20px;">' . $product_details . '</div>';

            // إضافة معلومات العرض المطبق (إذا وجد)
            $selected_offer_title = $order->get_meta('_selected_offer_title');
            $offer_discount = $order->get_meta('_offer_discount');

            if (!empty($selected_offer_title) && $offer_discount > 0) {
                $customer_message .= '<h3>العرض المطبق:</h3>';
                $customer_message .= sprintf('<p>العرض: %s</p>', $selected_offer_title);
                $customer_message .= sprintf('<p>قيمة الخصم: %s</p>', wc_price($offer_discount));
            }

            if ($shipping_total > 0) {
                $customer_message .= '<h3>معلومات الشحن:</h3>';
                foreach ($order->get_shipping_methods() as $shipping_method) {
                    $customer_message .= sprintf('<p>طريقة الشحن: %s - %s</p>', $shipping_method->get_method_title(), wc_price($shipping_method->get_total()));
                }
            }

            $customer_message .= '<h3>إجمالي الطلب:</h3>';
            $customer_message .= sprintf('<p>المجموع الفرعي: %s</p>', wc_price($order->get_subtotal()));
            if ($offer_discount > 0) {
                $customer_message .= sprintf('<p>الخصم: - %s</p>', wc_price($offer_discount));
            }
            if ($shipping_total > 0) {
                $customer_message .= sprintf('<p>الشحن: %s</p>', wc_price($shipping_total));
            }
            $customer_message .= sprintf('<p>الإجمالي: %s</p>', wc_price($order->get_total()));
            $customer_message .= '<p>سنتواصل معك قريباً لتأكيد طلبك.</p>';
            $customer_message .= '<p>شكراً لك!</p>';

            wp_mail($order->get_billing_email(), $customer_subject, $customer_message, $headers);
        }
    }

    /**
     * جلب متغيرات المنتج للاستخدام في النموذج
     *
     * @since    1.0.0
     */
    public function get_product_variations() {
        // التحقق من رمز الأمان
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pexlat_form_nonce')) {
            wp_send_json_error('رمز الأمان غير صالح. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
            exit;
        }

        // الحصول على معرف المنتج
        $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;

        if ($product_id <= 0) {
            wp_send_json_error('معرف المنتج غير صالح.');
            exit;
        }

        // الحصول على المنتج
        $product = wc_get_product($product_id);

        if (!$product || !$product->is_type('variable')) {
            wp_send_json_error('المنتج غير موجود أو ليس منتجًا متغيرًا.');
            exit;
        }

        // الحصول على جميع المتغيرات المتاحة
        $variations = $product->get_available_variations();
        $formatted_variations = array();

        foreach ($variations as $variation) {
            $variation_obj = wc_get_product($variation['variation_id']);

            if (!$variation_obj) {
                continue;
            }

            $formatted_variations[] = array(
                'variation_id' => $variation['variation_id'],
                'attributes' => $variation['attributes'],
                'display_price' => $variation_obj->get_price(),
                'display_regular_price' => $variation_obj->get_regular_price(),
                'is_in_stock' => $variation_obj->is_in_stock(),
                'price_html' => $variation['price_html'],
                'availability_html' => $variation['availability_html'],
                'image_id' => $variation['image_id'],
                'image_src' => $variation['image']['src'],
                'image_srcset' => $variation['image']['srcset'],
                'image_sizes' => $variation['image']['sizes']
            );
        }

        wp_send_json_success($formatted_variations);
        exit;
    }

    /**
     * التحقق من رمز الأمان (nonce) مع معالجة محسنة للأخطاء
     *
     * @since    1.0.4
     * @return   bool    true إذا كان الرمز صحيح، false إذا كان خاطئ
     */
    private function verify_security_token() {
        // التحقق من وجود الرمز
        if (!isset($_POST['nonce']) || empty($_POST['nonce'])) {
            error_log('Pexlat Form: رمز الأمان غير موجود في الطلب');
            return false;
        }

        // التحقق من صحة الرمز مع تسجيل تفاصيل إضافية
        $nonce_result = wp_verify_nonce($_POST['nonce'], 'pexlat_form_nonce');

        if (!$nonce_result) {
            error_log('Pexlat Form: فشل التحقق من رمز الأمان - Nonce: ' . $_POST['nonce']);
            error_log('Pexlat Form: User Agent: ' . ($_SERVER['HTTP_USER_AGENT'] ?? 'غير محدد'));
            error_log('Pexlat Form: IP Address: ' . ($_SERVER['REMOTE_ADDR'] ?? 'غير محدد'));
            return false;
        }

        return true;
    }

    /**
     * الحصول على رسالة خطأ الأمان المناسبة
     *
     * @since    1.5.1
     * @return   string    رسالة الخطأ
     */
    private function get_security_error_message() {
        // التحقق من وجود نظام حماية DZSecurity
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $is_dzsecurity = $this->is_dzsecurity_hosting();

        if ($is_dzsecurity) {
            return 'رمز الأمان غير صحيح. قد يكون هذا بسبب نظام الحماية. يرجى الانتظار قليلاً ثم تحديث الصفحة والمحاولة مرة أخرى.';
        }

        return 'رمز الأمان غير صحيح. يرجى تحديث الصفحة والمحاولة مرة أخرى.';
    }

    /**
     * التحقق من وجود نظام حماية DZSecurity
     *
     * @since    1.5.1
     * @return   bool
     */
    private function is_dzsecurity_hosting() {
        // التحقق من headers خاصة بـ DZSecurity
        $headers_to_check = array(
            'HTTP_CF_RAY', // Cloudflare
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP'
        );

        foreach ($headers_to_check as $header) {
            if (isset($_SERVER[$header])) {
                return true;
            }
        }

        // التحقق من domain أو server name
        $server_name = $_SERVER['SERVER_NAME'] ?? '';
        if (strpos($server_name, 'dzsecurity') !== false) {
            return true;
        }

        return false;
    }



    /**
     * ضمان تحديد طريقة الدفع الصحيحة في صفحة الدفع
     */
    public function set_payment_method_for_order_pay() {
        // التحقق من أننا في صفحة دفع طلب محدد
        if (!is_wc_endpoint_url('order-pay')) {
            return;
        }

        global $wp;

        // الحصول على معرف الطلب من URL
        $order_id = absint($wp->query_vars['order-pay']);

        if (!$order_id) {
            return;
        }

        // الحصول على الطلب
        $order = wc_get_order($order_id);

        if (!$order) {
            return;
        }

        // التحقق من أن هذا طلب من Pexlat Form
        $is_pexlat_order = isset($_GET['pexlat_form_order']) && $_GET['pexlat_form_order'] == '1';

        // الحصول على طريقة الدفع من URL أو من الطلب المحفوظ
        $payment_method_from_url = isset($_GET['payment_method']) ? sanitize_text_field($_GET['payment_method']) : '';
        $saved_payment_method = $order->get_payment_method();

        // استخدام طريقة الدفع من URL إذا كانت متوفرة، وإلا استخدم المحفوظة
        $target_payment_method = !empty($payment_method_from_url) ? $payment_method_from_url : $saved_payment_method;

        if (!empty($target_payment_method)) {
            // تعيين طريقة الدفع في الجلسة
            if (WC()->session) {
                WC()->session->set('chosen_payment_method', $target_payment_method);
            }

            // إضافة JavaScript لتحديد طريقة الدفع تلقائياً
            add_action('wp_footer', function() use ($target_payment_method, $is_pexlat_order) {
                ?>
                <script type="text/javascript">
                jQuery(document).ready(function($) {
                    // انتظار تحميل عناصر الدفع
                    setTimeout(function() {
                        // تحديد طريقة الدفع المحفوظة
                        var $targetMethod = $('input[name="payment_method"][value="<?php echo esc_js($target_payment_method); ?>"]');
                        if ($targetMethod.length) {
                            $targetMethod.prop('checked', true).trigger('change');

                            <?php if ($is_pexlat_order) : ?>
                            // إخفاء طرق الدفع الأخرى للطلبات من Pexlat Form
                            var $paymentMethods = $('input[name="payment_method"]');
                            if ($paymentMethods.length > 1) {
                                $paymentMethods.not('[value="<?php echo esc_js($target_payment_method); ?>"]').closest('li').hide();
                            }

                            // إضافة ملاحظة للمستخدم
                            var $paymentBox = $('.wc_payment_methods');
                            if ($paymentBox.length && !$paymentBox.find('.pexlat-payment-notice').length) {
                                $paymentBox.before('<div class="woocommerce-info pexlat-payment-notice">تم اختيار طريقة الدفع مسبقاً من النموذج: <strong><?php echo esc_js($target_payment_method); ?></strong></div>');
                            }
                            <?php endif; ?>
                        }
                    }, 500);
                });
                </script>
                <?php
            });
        }
    }

    /**
     * تنظيف المسودات القديمة
     */
    public static function cleanup_old_drafts() {
        global $wpdb;

        // الحصول على مدة الاحتفاظ من الإعدادات
        $cleanup_hours = intval(get_option('pexlat_form_abandoned_order_cleanup_hours', 24));
        $cleanup_seconds = $cleanup_hours * HOUR_IN_SECONDS;

        // البحث عن جميع خيارات المسودات
        $draft_options = $wpdb->get_results(
            "SELECT option_name, option_value FROM {$wpdb->options}
             WHERE option_name LIKE 'pexlat_form_draft_%'"
        );

        $cleaned_count = 0;

        foreach ($draft_options as $option) {
            $draft_info = maybe_unserialize($option->option_value);

            if (is_array($draft_info) && isset($draft_info['last_activity'])) {
                // حذف المسودات الأقدم من المدة المحددة
                if ($draft_info['last_activity'] < (time() - $cleanup_seconds)) {
                    // حذف الطلب من ووكومرس
                    if (isset($draft_info['order_id'])) {
                        wp_delete_post($draft_info['order_id'], true);
                    }

                    // حذف خيار المسودة
                    delete_option($option->option_name);
                    $cleaned_count++;
                }
            }
        }

        error_log("تم تنظيف {$cleaned_count} مسودة قديمة (أقدم من {$cleanup_hours} ساعة)");
    }
}

// إضافة hook لتنظيف المسودات القديمة
add_action('pexlat_form_cleanup_old_drafts', array('Pexlat_Form_Form_Handler', 'cleanup_old_drafts'));