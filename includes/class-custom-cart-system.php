<?php
/**
 * نظام السلة المخصص
 *
 * @since      1.0.0
 * @package    Pexlat_Form
 */

// منع الوصول المباشر
if (!defined('WPINC')) {
    die;
}

/**
 * صنف نظام السلة المخصص
 */
class Custom_Cart_System {

    /**
     * تهيئة الصنف
     */
    public function __construct() {
        // التحقق من تفعيل نظام السلة
        if (!$this->is_cart_system_enabled()) {
            return;
        }

        // إضافة hooks للواجهة الأمامية
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('wp_footer', array($this, 'add_cart_sidebar_html'));

        // إضافة أيقونة السلة العائمة (إذا كانت مفعلة)
        if ($this->is_cart_icon_enabled()) {
            add_action('wp_footer', array($this, 'add_floating_cart_icon'));
        }

        // إضافة AJAX handlers
        add_action('wp_ajax_add_to_custom_cart', array($this, 'handle_add_to_cart'));
        add_action('wp_ajax_nopriv_add_to_custom_cart', array($this, 'handle_add_to_cart'));
        add_action('wp_ajax_remove_from_custom_cart', array($this, 'handle_remove_from_cart'));
        add_action('wp_ajax_nopriv_remove_from_custom_cart', array($this, 'handle_remove_from_cart'));
        add_action('wp_ajax_update_cart_quantity', array($this, 'handle_update_quantity'));
        add_action('wp_ajax_nopriv_update_cart_quantity', array($this, 'handle_update_quantity'));
        add_action('wp_ajax_get_cart_contents', array($this, 'handle_get_cart_contents'));
        add_action('wp_ajax_nopriv_get_cart_contents', array($this, 'handle_get_cart_contents'));
        add_action('wp_ajax_complete_cart_order', array($this, 'handle_complete_order'));
        add_action('wp_ajax_nopriv_complete_cart_order', array($this, 'handle_complete_order'));


        // بدء الجلسة إذا لم تكن مبدوءة
        add_action('init', array($this, 'start_session'));
    }

    /**
     * التحقق من تفعيل نظام السلة
     */
    private function is_cart_system_enabled() {
        return get_option('pexlat_form_cart_system_enabled', 0) == 1;
    }

    /**
     * التحقق من تفعيل أيقونة السلة العائمة
     */
    private function is_cart_icon_enabled() {
        return get_option('pexlat_form_cart_icon_enabled', 1) == 1;
    }

    /**
     * التحقق من تفعيل فتح السلة تلقائياً
     */
    private function is_cart_auto_open_enabled() {
        return get_option('pexlat_form_cart_auto_open', 1) == 1;
    }

    /**
     * التحقق من تفعيل حفظ بيانات السلة
     */
    private function is_cart_save_data_enabled() {
        return get_option('pexlat_form_cart_save_data', 1) == 1;
    }

    /**
     * التحقق من تفعيل مسح السلة بعد الطلب
     */
    private function is_cart_clear_after_order_enabled() {
        return get_option('pexlat_form_cart_clear_after_order', 1) == 1;
    }

    /**
     * بدء الجلسة
     */
    public function start_session() {
        if (!session_id() && !headers_sent()) {
            session_start();
            error_log('تم بدء الجلسة بنجاح لنظام السلة');
        } elseif (session_id()) {
            error_log('الجلسة موجودة بالفعل: ' . session_id());
        } else {
            error_log('لا يمكن بدء الجلسة - تم إرسال الرؤوس بالفعل');
        }
    }

    /**
     * تحميل أصول الواجهة الأمامية
     */
    public function enqueue_frontend_assets() {
        // تحميل Font Awesome للأيقونات
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
            array(),
            '6.0.0'
        );

        // تحميل CSS
        wp_enqueue_style(
            'custom-cart-style',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/custom-cart.css',
            array('font-awesome'),
            PEXLAT_FORM_VERSION
        );

        // تحميل JavaScript
        wp_enqueue_script(
            'custom-cart-script',
            plugin_dir_url(dirname(__FILE__)) . 'public/js/custom-cart.js',
            array('jquery'),
            PEXLAT_FORM_VERSION,
            true
        );

        // إضافة متغيرات JavaScript
        wp_localize_script('custom-cart-script', 'customCart', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('custom_cart_nonce'),
            'currency' => 'دج',
            'settings' => array(
                'auto_open' => $this->is_cart_auto_open_enabled(),
                'save_data' => $this->is_cart_save_data_enabled(),
                'clear_after_order' => $this->is_cart_clear_after_order_enabled(),
                'default_button_text' => get_option('pexlat_form_cart_button_default_text', 'أضف إلى السلة'),
                'default_button_color' => get_option('pexlat_form_cart_button_default_color', '#28a745')
            ),
            'texts' => array(
                'added_to_cart' => 'تم إضافة المنتج إلى السلة',
                'removed_from_cart' => 'تم حذف المنتج من السلة',
                'cart_updated' => 'تم تحديث السلة',
                'error_occurred' => 'حدث خطأ، يرجى المحاولة مرة أخرى',
                'empty_cart' => 'السلة فارغة',
                'total' => 'المجموع',
                'shipping' => 'التوصيل',
                'final_total' => 'المجموع النهائي',
                'checkout' => 'إتمام الطلب',
                'continue_shopping' => 'متابعة التسوق'
            )
        ));
    }

    /**
     * إضافة أيقونة السلة العائمة
     */
    public function add_floating_cart_icon() {
        // الحصول على إعدادات الأيقونة العائمة
        $position = get_option('pexlat_form_floating_cart_position', 'top-left');
        $color = get_option('pexlat_form_floating_cart_color', '#28a745');
        $size = get_option('pexlat_form_floating_cart_size', 'medium');
        $shape = get_option('pexlat_form_floating_cart_shape', 'circle');

        ?>
        <div id="floating-cart-icon" class="floating-cart-icon <?php echo esc_attr($position); ?>">
            <div class="cart-icon-wrapper <?php echo esc_attr($size); ?> <?php echo esc_attr($shape); ?>"
                 style="background-color: <?php echo esc_attr($color); ?>; box-shadow: 0 4px 12px <?php echo esc_attr($color); ?>30;">
                <svg class="cart-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V16.5M9 19.5C9.8 19.5 10.5 20.2 10.5 21S9.8 22.5 9 22.5 7.5 21.8 7.5 21 8.2 19.5 9 19.5ZM20 19.5C20.8 19.5 21.5 20.2 21.5 21S20.8 22.5 20 22.5 18.5 21.8 18.5 21 19.2 19.5 20 19.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="cart-count">0</span>
            </div>
        </div>
        <?php
    }

    /**
     * إضافة HTML السلة الجانبية
     */
    public function add_cart_sidebar_html() {
        ?>
        <div id="cart-sidebar" class="cart-sidebar">
            <div class="cart-sidebar-overlay"></div>
            <div class="cart-sidebar-content">
                <div class="cart-sidebar-header">
                    <h3>سلة التسوق</h3>
                    <button class="cart-close-btn" type="button">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
                
                <div class="cart-sidebar-body">
                    <div class="cart-items-container">
                        <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                    </div>
                </div>
                
                <div class="cart-sidebar-footer">
                    <div class="cart-totals">
                        <div class="cart-subtotal">
                            <span>المجموع الفرعي:</span>
                            <span class="subtotal-amount">0 دج</span>
                        </div>
                        <div class="cart-shipping">
                            <span>التوصيل:</span>
                            <span class="shipping-amount">0 دج</span>
                        </div>
                        <div class="cart-total">
                            <span>المجموع النهائي:</span>
                            <span class="total-amount">0 دج</span>
                        </div>
                    </div>
                    
                    <div class="cart-actions">
                        <button class="btn-checkout" type="button">إتمام الطلب</button>
                        <button class="btn-continue-shopping" type="button">متابعة التسوق</button>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * معالجة إضافة منتج إلى السلة
     */
    public function handle_add_to_cart() {
        try {
            // التحقق من الأمان مع تسجيل تفاصيل إضافية
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'custom_cart_nonce')) {
                error_log('Custom Cart: فشل التحقق من رمز الأمان - Nonce: ' . ($_POST['nonce'] ?? 'غير موجود'));
                error_log('Custom Cart: User Agent: ' . ($_SERVER['HTTP_USER_AGENT'] ?? 'غير محدد'));
                wp_send_json_error('خطأ في التحقق من الأمان. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
                exit;
            }

            $product_id = intval($_POST['product_id']);
            $variation_id = intval($_POST['variation_id'] ?? 0);
            $quantity = intval($_POST['quantity']) ?: 1;

            // جمع بيانات العميل من النموذج
            $customer_data = array();

            // جمع جميع البيانات المرسلة من النموذج
            foreach ($_POST as $key => $value) {
                if (!in_array($key, array('action', 'nonce', 'product_id', 'variation_id', 'quantity'))) {
                    $customer_data[$key] = sanitize_text_field($value);
                }
            }

            // التحقق من صحة البيانات
            if ($product_id <= 0) {
                wp_send_json_error('معرف المنتج غير صالح');
                exit;
            }

            // تحديد المنتج المراد إضافته (المنتج الأساسي أو المتغير)
            $product_to_add = null;
            $actual_product_id = $product_id;
            $price = 0;

            if ($variation_id > 0) {
                // إذا كان هناك متغير، استخدم المتغير
                $variation_product = wc_get_product($variation_id);
                if ($variation_product && $variation_product->is_type('variation')) {
                    $product_to_add = $variation_product;
                    $actual_product_id = $variation_id;
                    $price = floatval($variation_product->get_price());
                    error_log("Custom Cart: استخدام متغير المنتج - ID: $variation_id, السعر: $price");
                } else {
                    wp_send_json_error('المتغير المحدد غير صالح');
                    exit;
                }
            } else {
                // استخدام المنتج الأساسي
                $product_to_add = wc_get_product($product_id);
                if (!$product_to_add) {
                    wp_send_json_error('المنتج غير موجود');
                    exit;
                }
                $price = floatval($product_to_add->get_price());
                error_log("Custom Cart: استخدام المنتج الأساسي - ID: $product_id, السعر: $price");
            }

            // حساب تكلفة التوصيل من طريقة التوصيل المختارة
            $shipping_cost = 0;
            if (isset($customer_data['shipping_method_option'])) {
                $shipping_cost = $this->get_shipping_cost_from_form($customer_data['shipping_method_option'], $customer_data, $product_id);
                error_log("Custom Cart: طريقة التوصيل المختارة: " . $customer_data['shipping_method_option']);
                error_log("Custom Cart: تكلفة التوصيل المحسوبة: " . $shipping_cost);
            } else {
                error_log("Custom Cart: لم يتم العثور على طريقة التوصيل في البيانات");
                error_log("Custom Cart: البيانات المرسلة: " . print_r($customer_data, true));
            }

            // إضافة المنتج إلى السلة
            $cart_item = array(
                'product_id' => $product_id,
                'variation_id' => $variation_id,
                'actual_product_id' => $actual_product_id, // المعرف الفعلي (المنتج أو المتغير)
                'quantity' => $quantity,
                'price' => $price,
                'name' => $product_to_add->get_name(),
                'image' => wp_get_attachment_image_url($product_to_add->get_image_id(), 'thumbnail'),
                'customer_data' => $customer_data,
                'shipping_cost' => $shipping_cost,
                'added_time' => current_time('timestamp')
            );

            // الحصول على السلة الحالية
            $cart = $this->get_cart();

            // البحث عن المنتج في السلة (مع مراعاة المتغيرات)
            $found = false;
            foreach ($cart as $key => $item) {
                // مقارنة المعرف الفعلي للمنتج (يشمل المتغيرات)
                if ($item['actual_product_id'] == $actual_product_id) {
                    $cart[$key]['quantity'] += $quantity;
                    // تحديث بيانات العميل وتكلفة التوصيل
                    $cart[$key]['customer_data'] = $customer_data;
                    $cart[$key]['shipping_cost'] = $shipping_cost;
                    $found = true;
                    break;
                }
            }

            // إذا لم يوجد المنتج، أضفه
            if (!$found) {
                $cart[] = $cart_item;
            }

            // حفظ السلة
            $this->save_cart($cart);

            wp_send_json_success(array(
                'message' => 'تم إضافة المنتج إلى السلة',
                'cart_count' => $this->get_cart_count(),
                'cart_total' => $this->get_cart_total(),
                'shipping_total' => $this->get_cart_shipping_total()
            ));

        } catch (Exception $e) {
            error_log('خطأ في إضافة المنتج إلى السلة: ' . $e->getMessage());
            wp_send_json_error('حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * معالجة حذف منتج من السلة
     */
    public function handle_remove_from_cart() {
        try {
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'custom_cart_nonce')) {
                wp_send_json_error('خطأ في التحقق من الأمان');
                exit;
            }

        $product_id = intval($_POST['product_id']);
        $cart = $this->get_cart();

        // حذف المنتج من السلة (مع مراعاة المتغيرات)
        foreach ($cart as $key => $item) {
            // استخدام actual_product_id للمقارنة لدعم المتغيرات
            $item_actual_id = isset($item['actual_product_id']) ? $item['actual_product_id'] : $item['product_id'];
            if ($item_actual_id == $product_id) {
                unset($cart[$key]);
                break;
            }
        }

        // إعادة ترقيم المصفوفة
        $cart = array_values($cart);
        $this->save_cart($cart);

        wp_send_json_success(array(
            'message' => 'تم حذف المنتج من السلة',
            'cart_count' => $this->get_cart_count(),
            'cart_total' => $this->get_cart_total()
        ));

        } catch (Exception $e) {
            error_log('خطأ في حذف المنتج من السلة: ' . $e->getMessage());
            wp_send_json_error('حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * الحصول على السلة
     */
    private function get_cart() {
        $cart = isset($_SESSION['custom_cart']) ? $_SESSION['custom_cart'] : array();

        // تحديث السلة القديمة لدعم المتغيرات
        foreach ($cart as $key => $item) {
            if (!isset($item['actual_product_id'])) {
                $cart[$key]['actual_product_id'] = $item['product_id'];
                $cart[$key]['variation_id'] = 0;
            }
        }

        return $cart;
    }

    /**
     * حفظ السلة
     */
    private function save_cart($cart) {
        $_SESSION['custom_cart'] = $cart;
    }

    /**
     * الحصول على عدد المنتجات في السلة
     */
    private function get_cart_count() {
        $cart = $this->get_cart();
        $count = 0;
        foreach ($cart as $item) {
            $count += $item['quantity'];
        }
        return $count;
    }

    /**
     * الحصول على إجمالي السلة
     */
    private function get_cart_total() {
        $cart = $this->get_cart();
        $total = 0;
        foreach ($cart as $item) {
            $total += $item['price'] * $item['quantity'];
        }
        return $total;
    }

    /**
     * الحصول على إجمالي تكلفة التوصيل للسلة
     * يتم حساب التكلفة بناءً على أول منتج تمت إضافته إلى السلة
     */
    private function get_cart_shipping_total() {
        $cart = $this->get_cart();

        if (empty($cart)) {
            return 0;
        }

        // الحصول على تكلفة التوصيل من أول منتج في السلة
        $first_item = reset($cart);
        return floatval($first_item['shipping_cost'] ?? 0);
    }

    /**
     * الحصول على تكلفة التوصيل من طريقة التوصيل المختارة في النموذج
     */
    private function get_shipping_cost_from_form($shipping_method_option, $customer_data = array(), $product_id = 0) {
        // أولاً، نحاول البحث عن التكلفة في البيانات المرسلة
        // البحث عن حقل يحتوي على التكلفة
        foreach ($customer_data as $key => $value) {
            // البحث عن حقول تحتوي على "cost" أو "price" أو "shipping"
            if (strpos(strtolower($key), 'cost') !== false ||
                strpos(strtolower($key), 'price') !== false ||
                strpos(strtolower($key), 'shipping') !== false) {
                if (is_numeric($value)) {
                    $base_cost = floatval($value);
                    return $base_cost;
                }
            }
        }

        // إذا لم نجد التكلفة في البيانات، نحاول الحصول عليها من إعدادات النموذج
        $shipping_cost = $this->get_shipping_cost_by_method($shipping_method_option);

        if ($shipping_cost > 0) {
            return $shipping_cost;
        }

        // تحليل قيمة طريقة التوصيل للحصول على التكلفة
        // القيمة قد تأتي بصيغة: "company_id:cost" أو مجرد cost
        if (strpos($shipping_method_option, ':') !== false) {
            $parts = explode(':', $shipping_method_option);
            $base_cost = floatval(end($parts));
            return $base_cost;
        }

        // إذا كانت القيمة رقم مباشر
        if (is_numeric($shipping_method_option)) {
            $base_cost = floatval($shipping_method_option);
            return $base_cost;
        }

        return 0;
    }

    /**
     * الحصول على تكلفة التوصيل حسب طريقة التوصيل من إعدادات النموذج
     */
    private function get_shipping_cost_by_method($shipping_method) {
        // الحصول على إعدادات النموذج
        $form_settings = get_option('pexlat_form_settings', array());

        switch ($shipping_method) {
            case 'standard_shipping':
                return floatval($form_settings['default_shipping_1_cost'] ?? 0);

            case 'economy_shipping':
                return floatval($form_settings['default_shipping_2_cost'] ?? 0);

            case 'free_shipping':
                return 0;

            default:
                // محاولة البحث في بيانات شركات الشحن
                $shipping_companies_data = get_option('pexlat_form_shipping_companies_data', array());

                if (isset($shipping_companies_data[$shipping_method])) {
                    $company_data = $shipping_companies_data[$shipping_method];

                    // إرجاع أول تكلفة متاحة
                    if (isset($company_data['pricing']) && is_array($company_data['pricing'])) {
                        foreach ($company_data['pricing'] as $pricing_item) {
                            if (isset($pricing_item['price']) && $pricing_item['price'] > 0) {
                                return floatval($pricing_item['price']);
                            }
                        }
                    }
                }

                return 0;
        }
    }



    /**
     * معالجة تحديث كمية المنتج
     */
    public function handle_update_quantity() {
        if (!wp_verify_nonce($_POST['nonce'], 'custom_cart_nonce')) {
            wp_send_json_error('خطأ في التحقق من الأمان');
            exit;
        }

        $product_id = intval($_POST['product_id']);
        $quantity = intval($_POST['quantity']);

        if ($quantity <= 0) {
            wp_send_json_error('الكمية يجب أن تكون أكبر من صفر');
            exit;
        }

        $cart = $this->get_cart();

        // تحديث كمية المنتج (مع مراعاة المتغيرات)
        foreach ($cart as $key => $item) {
            // استخدام actual_product_id للمقارنة لدعم المتغيرات
            $item_actual_id = isset($item['actual_product_id']) ? $item['actual_product_id'] : $item['product_id'];
            if ($item_actual_id == $product_id) {
                $cart[$key]['quantity'] = $quantity;
                break;
            }
        }

        $this->save_cart($cart);

        wp_send_json_success(array(
            'message' => 'تم تحديث الكمية',
            'cart_count' => $this->get_cart_count(),
            'cart_total' => $this->get_cart_total()
        ));
    }

    /**
     * معالجة الحصول على محتويات السلة
     */
    public function handle_get_cart_contents() {
        if (!wp_verify_nonce($_POST['nonce'], 'custom_cart_nonce')) {
            wp_send_json_error('خطأ في التحقق من الأمان');
            exit;
        }

        $cart = $this->get_cart();
        $cart_html = $this->generate_cart_html($cart);
        $shipping_total = $this->get_cart_shipping_total();
        $subtotal = $this->get_cart_total();
        $total = $subtotal + $shipping_total;

        wp_send_json_success(array(
            'cart_html' => $cart_html,
            'cart_count' => $this->get_cart_count(),
            'subtotal' => $subtotal,
            'shipping' => $shipping_total,
            'total' => $total,
            'empty' => empty($cart)
        ));
    }

    /**
     * توليد HTML للسلة
     */
    private function generate_cart_html($cart) {
        if (empty($cart)) {
            return '<div class="empty-cart">
                        <div class="empty-cart-icon">🛒</div>
                        <p>السلة فارغة</p>
                        <small>أضف منتجات لتبدأ التسوق</small>
                    </div>';
        }

        $html = '<div class="cart-items">';

        // الحصول على تكلفة التوصيل من أول منتج فقط
        $first_item = reset($cart);
        $main_shipping_cost = floatval($first_item['shipping_cost'] ?? 0);

        foreach ($cart as $index => $item) {
            $product_total = $item['price'] * $item['quantity'];
            $image_html = $item['image'] ? '<img src="' . esc_url($item['image']) . '" alt="' . esc_attr($item['name']) . '">' : '<div class="no-image">لا توجد صورة</div>';

            // عرض بيانات العميل
            $customer_info_html = '';
            if (!empty($item['customer_data'])) {
                $customer_info_html = '<div class="customer-info-summary">';

                // الاسم الكامل
                if (!empty($item['customer_data']['first_name']) || !empty($item['customer_data']['last_name'])) {
                    $full_name = trim(($item['customer_data']['first_name'] ?? '') . ' ' . ($item['customer_data']['last_name'] ?? ''));
                    if ($full_name) {
                        $customer_info_html .= '<div class="info-item"><strong>العميل:</strong> ' . esc_html($full_name) . '</div>';
                    }
                }

                // الهاتف
                if (!empty($item['customer_data']['phone'])) {
                    $customer_info_html .= '<div class="info-item"><strong>الهاتف:</strong> ' . esc_html($item['customer_data']['phone']) . '</div>';
                }

                // الولاية والبلدية
                $location_parts = array();
                if (!empty($item['customer_data']['municipality'])) {
                    $location_parts[] = $item['customer_data']['municipality'];
                }
                if (!empty($item['customer_data']['state'])) {
                    $location_parts[] = $item['customer_data']['state'];
                }
                if (!empty($item['customer_data']['city'])) {
                    $location_parts[] = $item['customer_data']['city'];
                }

                if (!empty($location_parts)) {
                    $location = implode(', ', $location_parts);
                    $customer_info_html .= '<div class="info-item"><strong>الموقع:</strong> ' . esc_html($location) . '</div>';
                }

                // العنوان التفصيلي
                if (!empty($item['customer_data']['address'])) {
                    $customer_info_html .= '<div class="info-item"><strong>العنوان:</strong> ' . esc_html($item['customer_data']['address']) . '</div>';
                }

                // معلومات إضافية أخرى
                $additional_fields = array(
                    'email' => 'البريد الإلكتروني',
                    'notes' => 'ملاحظات',
                    'company' => 'الشركة',
                    'postal_code' => 'الرمز البريدي'
                );

                foreach ($additional_fields as $field => $label) {
                    if (!empty($item['customer_data'][$field])) {
                        $customer_info_html .= '<div class="info-item"><strong>' . $label . ':</strong> ' . esc_html($item['customer_data'][$field]) . '</div>';
                    }
                }

                // عرض تكلفة التوصيل فقط للمنتج الأول
                if ($index === 0 && $main_shipping_cost > 0) {
                    $customer_info_html .= '<div class="info-item shipping-info"><strong>التوصيل:</strong> ' . number_format($main_shipping_cost, 2) . ' دج</div>';
                }

                $customer_info_html .= '</div>';
            }

            // استخدام المعرف الفعلي للمنتج (يدعم المتغيرات)
            $item_actual_id = isset($item['actual_product_id']) ? $item['actual_product_id'] : $item['product_id'];

            $html .= '
            <div class="cart-item" data-product-id="' . $item_actual_id . '">
                <div class="item-image">' . $image_html . '</div>
                <div class="item-details">
                    <h4 class="item-name">' . esc_html($item['name']) . '</h4>
                    <div class="item-price">' . number_format($item['price'], 2) . ' دج</div>
                    ' . $customer_info_html . '
                    <div class="item-quantity">
                        <button class="qty-btn qty-minus" type="button">-</button>
                        <input type="number" class="qty-input" value="' . $item['quantity'] . '" min="1">
                        <button class="qty-btn qty-plus" type="button">+</button>
                    </div>
                </div>
                <div class="item-total">
                    <div class="total-price">' . number_format($product_total, 2) . ' دج</div>
                    <button class="remove-item" type="button" title="حذف المنتج">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
            </div>';
        }

        $html .= '</div>';
        return $html;
    }



    /**
     * معالجة إتمام الطلب
     */
    public function handle_complete_order() {
        // تنظيف أي output سابق
        if (ob_get_level()) {
            ob_clean();
        }

        try {
            if (!wp_verify_nonce($_POST['nonce'], 'custom_cart_nonce')) {
                wp_send_json_error('خطأ في التحقق من الأمان');
                exit;
            }

            $cart = $this->get_cart();

            if (empty($cart)) {
                wp_send_json_error('السلة فارغة');
                exit;
            }

            // التحقق من العميل المحظور والطلبات المتكررة
            $validation_result = $this->validate_cart_order();
            if ($validation_result !== true) {
                wp_send_json_error($validation_result);
                exit;
            }

            // الحصول على طريقة الدفع المختارة
            $selected_payment_method = isset($_POST['payment_method']) ? sanitize_text_field($_POST['payment_method']) : '';

            // البحث عن طلب مسودة موجود قبل إنشاء طلب جديد
            $order = $this->find_or_create_cart_order($cart);

            if (!$order || is_wp_error($order)) {
                error_log('فشل في إنشاء الطلب: ' . (is_wp_error($order) ? $order->get_error_message() : 'خطأ غير معروف'));
                wp_send_json_error('فشل في إنشاء الطلب');
                exit;
            }

            // تسجيل معرف الطلب للتصحيح
            error_log('تم إنشاء/تحديث الطلب بنجاح، معرف الطلب: ' . $order->get_id());

        // إضافة المنتجات إلى الطلب
        $customer_data = array();
        $first_item = reset($cart);
        $total_shipping = floatval($first_item['shipping_cost'] ?? 0); // تكلفة التوصيل من أول منتج فقط

        foreach ($cart as $item) {
            // تحديد المنتج المراد إضافته (المنتج الأساسي أو المتغير)
            $product_to_add = null;
            if (!empty($item['variation_id']) && $item['variation_id'] > 0) {
                // إضافة المتغير
                $product_to_add = wc_get_product($item['variation_id']);
            } else {
                // إضافة المنتج الأساسي
                $product_to_add = wc_get_product($item['product_id']);
            }

            if ($product_to_add) {
                $order->add_product($product_to_add, $item['quantity']);

                // جمع بيانات العميل من أول منتج
                if (empty($customer_data) && !empty($item['customer_data'])) {
                    $customer_data = $item['customer_data'];
                }

                // إضافة بيانات العميل كـ meta data للمنتج
                if (!empty($item['customer_data'])) {
                    $product_meta_key = '_customer_data_' . $item['actual_product_id'];
                    $order->add_meta_data($product_meta_key, $item['customer_data']);

                    // إضافة بيانات النموذج لكل منتج بنفس تنسيق النظام المباشر مع أسماء حقول صحيحة
                    $item_form_data = array();

                    // تعيين أسماء الحقول بالعربية
                    $field_labels = array(
                        'full_name' => 'الاسم الكامل',
                        'first_name' => 'الاسم الأول',
                        'last_name' => 'الاسم الأخير',
                        'phone' => 'رقم الهاتف',
                        'address' => 'العنوان التفصيلي',
                        'state' => 'الولاية',
                        'municipality' => 'البلدية',
                        'municipality_text' => 'البلدية',
                        'city' => 'المدينة',
                        'email' => 'البريد الإلكتروني',
                        'notes' => 'ملاحظات',
                        'company' => 'الشركة',
                        'postal_code' => 'الرمز البريدي',
                        'shipping_method_option' => 'طريقة الشحن',
                        'shipping_method_name' => 'اسم طريقة الشحن',
                        'quantity' => 'الكمية',
                        'product_id' => 'معرف المنتج',
                        'variation_id' => 'معرف المتغير'
                    );

                    foreach ($item['customer_data'] as $key => $value) {
                        if (!empty($value)) {
                            // الحصول على اسم الحقل الصحيح
                            $label = $this->get_field_label($key, $field_labels);
                            $item_form_data[$key] = array(
                                'label' => $label,
                                'value' => $value,
                                'type' => 'text'
                            );
                        }
                    }

                    // حفظ بيانات النموذج لكل منتج
                    $form_meta_key = '_pexlat_form_data_' . $item['actual_product_id'];
                    $order->add_meta_data($form_meta_key, $item_form_data);
                }
            }
        }

        // إضافة بيانات العميل إلى الطلب مع تحويل أسماء الحقول
        if (!empty($customer_data)) {
            // تحويل أسماء الحقول لتتطابق مع النظام المباشر
            $full_name = isset($customer_data['full_name']) ? $customer_data['full_name'] : '';
            $phone = isset($customer_data['phone']) ? $customer_data['phone'] : '';
            $address = isset($customer_data['address']) ? $customer_data['address'] : '';
            $state = isset($customer_data['state']) ? $customer_data['state'] : '';
            $municipality = isset($customer_data['municipality']) ? $customer_data['municipality'] : '';

            // تقسيم الاسم الكامل إلى اسم أول وأخير
            $name_parts = explode(' ', trim($full_name), 2);
            $first_name = isset($name_parts[0]) ? $name_parts[0] : '';
            $last_name = isset($name_parts[1]) ? $name_parts[1] : '';

            // الحصول على اسم الولاية الكامل مع الرقم
            $state_name = '';
            if (!empty($state)) {
                // تضمين ملف بيانات الولايات
                require_once plugin_dir_path(dirname(__FILE__)) . 'includes/algeria-cities.php';
                $state_only_name = get_state_name($state);
                // عرض الرقم مع الاسم (مثل: 19 سطيف)
                $state_name = $state . ' ' . $state_only_name;
            }

            // الحصول على اسم البلدية الكامل
            $municipality_name = '';

            // أولاً نحاول الحصول على اسم البلدية من حقل النص المخفي
            if (isset($customer_data['municipality_text']) && !empty($customer_data['municipality_text'])) {
                $municipality_name = $customer_data['municipality_text'];
            }

            // إذا كان لا يزال فارغاً، نستخدم وظيفة الاسترجاع
            if (empty($municipality_name) && !empty($municipality) && !empty($state)) {
                // تضمين ملف بيانات البلديات إذا لم يكن مضمناً بعد
                if (!function_exists('get_municipality_name')) {
                    require_once plugin_dir_path(dirname(__FILE__)) . 'includes/algeria-cities.php';
                }
                $municipality_name = get_municipality_name($municipality, $state);
            }

            // إعداد عناوين الدفع والشحن مع استخدام اسم الولاية
            $billing_data = array(
                'first_name' => $first_name,
                'last_name'  => $last_name,
                'phone'      => $phone,
                'country'    => 'DZ',
                'state'      => $state_name, // استخدام اسم الولاية بدلاً من رقمها
                'city'       => $municipality_name, // استخدام الاسم الكامل للبلدية
                'address_1'  => $address
            );

            $order->set_address($billing_data, 'billing');
            $order->set_address($billing_data, 'shipping');

            // حفظ بيانات العميل في meta_data مع استخدام اسم الولاية
            $order_meta = array(
                '_billing_first_name'  => $first_name,
                '_billing_last_name'   => $last_name,
                '_billing_phone'       => $phone,
                '_billing_country'     => 'DZ',
                '_billing_state'       => $state_name, // استخدام اسم الولاية بدلاً من رقمها
                '_billing_city'        => $municipality_name, // استخدام الاسم الكامل للبلدية
                '_billing_address_1'   => $address,
                '_shipping_first_name' => $first_name,
                '_shipping_last_name'  => $last_name,
                '_shipping_country'    => 'DZ',
                '_shipping_state'      => $state_name, // استخدام اسم الولاية بدلاً من رقمها
                '_shipping_city'       => $municipality_name, // استخدام الاسم الكامل للبلدية
                '_shipping_address_1'  => $address
            );

            foreach ($order_meta as $key => $value) {
                $order->update_meta_data($key, $value);
            }

            // حفظ بيانات إضافية مع الاسم المقسم واسم الولاية
            $order->update_meta_data('_customer_full_name', $full_name);
            $order->update_meta_data('_customer_first_name', $first_name);
            $order->update_meta_data('_customer_last_name', $last_name);
            $order->update_meta_data('_state_name', $state_name);
            $order->update_meta_data('_state_code', $state);
        }

        // إضافة تكلفة التوصيل إذا كانت موجودة
        if ($total_shipping > 0) {
            $shipping_item = new WC_Order_Item_Shipping();

            // التحقق من اسم طريقة الشحن
            $method_title = 'توصيل للمنزل'; // القيمة الافتراضية

            // البحث عن اسم طريقة الشحن في بيانات العميل
            if (!empty($customer_data)) {
                // البحث عن اسم طريقة الشحن بأولوية
                $shipping_method_keys = array('shipping_method_name', 'shipping_method_title', 'shipping_method');

                foreach ($shipping_method_keys as $key) {
                    if (isset($customer_data[$key]) && !empty($customer_data[$key]) && $customer_data[$key] !== 'undefined') {
                        $method_title = $customer_data[$key];
                        break;
                    }
                }

                // إذا لم نجد اسم طريقة الشحن، نبحث في جميع البيانات
                if ($method_title === 'توصيل للمنزل') {
                    foreach ($customer_data as $key => $value) {
                        if (strpos(strtolower($key), 'shipping') !== false &&
                            !empty($value) &&
                            $value !== 'undefined' &&
                            !is_numeric($value)) {
                            $method_title = $value;
                            break;
                        }
                    }
                }
            }

            $shipping_item->set_method_title($method_title);
            $shipping_item->set_method_id('custom_shipping');
            $shipping_item->set_total($total_shipping);
            $order->add_item($shipping_item);
        }

        // حفظ بيانات النموذج الكاملة في meta_data الخاصة بالطلب
        if (!empty($customer_data)) {
            // تحويل بيانات العميل إلى تنسيق مشابه للنظام المباشر مع أسماء حقول صحيحة
            $form_data = array();

            // تعيين أسماء الحقول بالعربية
            $field_labels = array(
                'full_name' => 'الاسم الكامل',
                'first_name' => 'الاسم الأول',
                'last_name' => 'الاسم الأخير',
                'phone' => 'رقم الهاتف',
                'address' => 'العنوان التفصيلي',
                'state' => 'الولاية',
                'municipality' => 'البلدية',
                'municipality_text' => 'البلدية',
                'city' => 'المدينة',
                'email' => 'البريد الإلكتروني',
                'notes' => 'ملاحظات',
                'company' => 'الشركة',
                'postal_code' => 'الرمز البريدي',
                'shipping_method_option' => 'طريقة الشحن',
                'shipping_method_name' => 'اسم طريقة الشحن',
                'quantity' => 'الكمية',
                'product_id' => 'معرف المنتج',
                'variation_id' => 'معرف المتغير'
            );

            foreach ($customer_data as $key => $value) {
                if (!empty($value)) {
                    // الحصول على اسم الحقل الصحيح
                    $label = $this->get_field_label($key, $field_labels);
                    $form_data[$key] = array(
                        'label' => $label,
                        'value' => $value,
                        'type' => 'text'
                    );
                }
            }

            // تنظيف بيانات النموذج من الحقول التقنية قبل الحفظ
            $cleaned_form_data = $this->clean_form_data_for_storage($form_data);

            // حفظ بيانات النموذج
            $order->update_meta_data('_pexlat_form_data', $cleaned_form_data);

            // تم تعطيل حفظ البيانات في جدول الإرسالات لأن الجدول لم يعد مستخدماً
            // $this->save_cart_submission_to_database($form_data, $order->get_id());
        }

        // معالجة طريقة الدفع
        $needs_payment_processing = false;
        if (!empty($selected_payment_method) && class_exists('WooCommerce')) {
            $available_gateways = WC()->payment_gateways->get_available_payment_gateways();
            if (isset($available_gateways[$selected_payment_method])) {
                $gateway = $available_gateways[$selected_payment_method];

                // حفظ طريقة الدفع في الطلب
                $order->set_payment_method($selected_payment_method);
                $order->set_payment_method_title($gateway->get_title());

                // حفظ معلومات إضافية عن طريقة الدفع
                $order->update_meta_data('_pexlat_form_payment_method', $selected_payment_method);
                $order->update_meta_data('_pexlat_form_payment_method_title', $gateway->get_title());

                // حفظ طريقة الدفع في الجلسة أيضاً لضمان التحديد التلقائي
                if (WC()->session) {
                    WC()->session->set('chosen_payment_method', $selected_payment_method);
                }

                // طرق الدفع التي لا تحتاج معالجة فورية
                $direct_payment_methods = array('cod', 'bacs'); // الدفع عند الاستلام والتحويل البنكي

                if (!in_array($selected_payment_method, $direct_payment_methods)) {
                    $needs_payment_processing = true;
                }
            }
        }

        // حساب المجاميع
        $order->calculate_totals();

        // تعيين حالة الطلب حسب طريقة الدفع
        if ($needs_payment_processing) {
            // إذا كان الطلب يحتاج دفع، نضعه في حالة "في انتظار الدفع"
            $order->set_status('pending', 'طلب من السلة المخصصة - في انتظار الدفع');
        } else {
            // تعيين حالة الطلب إلى "قيد المعالجة" مثل النظام المباشر
            $order->set_status('processing', 'طلب من السلة المخصصة');
        }

        // حفظ الطلب
        $order_id = $order->save();

        if (!$order_id) {
            error_log('فشل في حفظ الطلب');
            wp_send_json_error('فشل في حفظ الطلب');
            exit;
        }

        // تسجيل نجاح حفظ الطلب
        error_log('تم حفظ الطلب بنجاح، معرف الطلب: ' . $order_id);

        // حذف مفتاح المسودة إذا كان موجوداً
        $this->cleanup_draft_key($customer_data);

        // إرسال إشعارات البريد الإلكتروني مثل النظام المباشر
        if (!empty($customer_data)) {
            $this->send_cart_notification($customer_data, $order);
        }

        // تحديد رابط إعادة التوجيه حسب حالة الطلب
        if ($needs_payment_processing) {
            // توجيه المستخدم إلى صفحة الدفع المحددة للطلب
            $redirect_url = $order->get_checkout_payment_url();

            // إضافة معاملات إضافية لضمان تحديد طريقة الدفع
            $redirect_url = add_query_arg(array(
                'payment_method' => $selected_payment_method,
                'pexlat_form_order' => '1'
            ), $redirect_url);

            $success_message = 'تم إنشاء طلبك بنجاح. سيتم توجيهك لإكمال الدفع عبر ' . (isset($gateway) ? $gateway->get_title() : 'طريقة الدفع المختارة');
        } else {
            // الحصول على رابط صفحة الشكر
            $redirect_url = $order->get_checkout_order_received_url();
            $success_message = 'تم إنشاء الطلب بنجاح';
        }

        error_log('رابط إعادة التوجيه: ' . $redirect_url);

        // تحديث سجل الطلبات بعد نجاح الطلب
        $this->update_order_history($_SERVER['REMOTE_ADDR']);

        // مسح السلة
        $this->clear_cart();

        // إرسال الاستجابة الناجحة
        $response_data = array(
            'message' => $success_message,
            'order_id' => $order->get_id(),
            'redirect_url' => $redirect_url,
            'needs_payment' => $needs_payment_processing
        );

        if ($needs_payment_processing && !empty($selected_payment_method)) {
            $response_data['payment_method'] = $selected_payment_method;
            $response_data['payment_method_title'] = isset($gateway) ? $gateway->get_title() : '';
        }

        wp_send_json_success($response_data);

        // التأكد من إنهاء التنفيذ
        exit;

        } catch (Exception $e) {
            error_log('خطأ في إتمام الطلب عبر السلة: ' . $e->getMessage());
            error_log('تفاصيل الخطأ: ' . $e->getTraceAsString());

            // إرسال استجابة خطأ مع تفاصيل أكثر للتصحيح
            wp_send_json_error(array(
                'message' => 'حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.',
                'debug_info' => WP_DEBUG ? $e->getMessage() : null
            ));

            // التأكد من إنهاء التنفيذ
            exit;
        }
    }



    /**
     * مسح السلة
     */
    private function clear_cart() {
        unset($_SESSION['custom_cart']);
    }

    /**
     * الحصول على اسم الحقل الصحيح من معرف الحقل
     *
     * @since    1.0.0
     * @param    string   $field_id       معرف الحقل
     * @param    array    $field_labels   مصفوفة أسماء الحقول المعرفة مسبقاً
     * @return   string                   اسم الحقل الصحيح
     */
    private function get_field_label($field_id, $field_labels) {
        // إذا كان الحقل موجود في القائمة المعرفة مسبقاً
        if (isset($field_labels[$field_id])) {
            return $field_labels[$field_id];
        }

        // إذا كان معرف الحقل يحتوي على معرف تقني (مثل: Field_1750619437150_jg40xyt75)
        if (preg_match('/^Field[_\s]+\d+[_\s]+([a-zA-Z0-9]+)$/i', $field_id, $matches)) {
            // محاولة الحصول على اسم الحقل من النموذج الأصلي
            $field_name = $this->get_field_name_from_form($matches[1]);
            if (!empty($field_name)) {
                return $field_name;
            }
        }

        // إذا كان معرف الحقل يحتوي على نمط آخر، نحاول استخراج اسم مفهوم
        if (strpos($field_id, 'Field') === 0) {
            // إزالة "Field" والأرقام والرموز التقنية
            $clean_name = preg_replace('/^Field[_\s]*\d*[_\s]*[a-zA-Z0-9]*[_\s]*/', '', $field_id);
            if (!empty($clean_name)) {
                return ucfirst(str_replace('_', ' ', $clean_name));
            }

            // إذا لم نجد اسم واضح، نستخدم اسم افتراضي
            return 'حقل مخصص';
        }

        // الافتراضي: تحويل معرف الحقل إلى اسم مقروء
        return ucfirst(str_replace('_', ' ', $field_id));
    }

    /**
     * الحصول على اسم الحقل من النموذج الأصلي باستخدام معرف الحقل التقني
     *
     * @since    1.0.0
     * @param    string   $field_tech_id   المعرف التقني للحقل
     * @return   string                    اسم الحقل أو فارغ إذا لم يوجد
     */
    private function get_field_name_from_form($field_tech_id) {
        global $wpdb;

        try {
            // البحث في جدول النماذج عن الحقل
            $table_name = $wpdb->prefix . 'pexlat_form_forms';
            $forms = $wpdb->get_results("SELECT fields FROM {$table_name} WHERE status = 'active'");

            foreach ($forms as $form) {
                $fields = Pexlat_Form_Helper::unserialize_data($form->fields);

                if (is_array($fields)) {
                    foreach ($fields as $field) {
                        // التحقق من وجود معرف تقني مطابق
                        if (isset($field['id']) && strpos($field['id'], $field_tech_id) !== false) {
                            // إرجاع اسم الحقل إذا وجد
                            if (isset($field['label']) && !empty($field['label'])) {
                                return $field['label'];
                            }
                        }
                    }
                }
            }
        } catch (Exception $e) {
            error_log('خطأ في البحث عن اسم الحقل: ' . $e->getMessage());
        }

        return '';
    }

    /**
     * تنظيف بيانات النموذج من الحقول التقنية قبل الحفظ
     *
     * @since    1.0.0
     * @param    array    $form_data    بيانات النموذج الأصلية
     * @return   array                  بيانات النموذج المنظفة
     */
    private function clean_form_data_for_storage($form_data) {
        if (!is_array($form_data)) {
            return $form_data;
        }

        // قائمة الحقول التقنية التي يجب إزالتها من التخزين
        $technical_fields = array(
            'wp_http_referer', '_wp_http_referer', 'action', 'nonce', '_wpnonce',
            'form_id', 'product_id', 'variation_id', 'quantity'
        );

        $cleaned_data = array();

        foreach ($form_data as $field_id => $field_data) {
            // تخطي الحقول التقنية
            if (in_array($field_id, $technical_fields)) {
                continue;
            }

            // الاحتفاظ بالحقول الأخرى
            $cleaned_data[$field_id] = $field_data;
        }

        return $cleaned_data;
    }

    /**
     * حفظ بيانات السلة في جدول الإرسالات
     */
    private function save_cart_submission_to_database($form_data, $order_id) {
        try {
            global $wpdb;

            // تحديد معرف النموذج (افتراضي 1 إذا لم يكن محدد)
            $form_id = 1;

            // البحث عن معرف النموذج في البيانات
            if (isset($form_data['form_id'])) {
                $form_id = intval($form_data['form_id']['value']);
            }

            // تحديد معرف المستخدم
            $user_id = get_current_user_id();

            // إدراج الإرسال
            $result = $wpdb->insert(
                $wpdb->prefix . 'pexlat_form_submissions',
                array(
                    'form_id' => $form_id,
                    'data' => Pexlat_Form_Helper::serialize_data($form_data),
                    'order_id' => $order_id,
                    'user_id' => $user_id > 0 ? $user_id : null,
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql'),
                ),
                array('%d', '%s', '%d', '%d', '%s', '%s')
            );

            if ($result === false) {
                error_log('فشل في حفظ بيانات السلة في جدول الإرسالات: ' . $wpdb->last_error);
            }

        } catch (Exception $e) {
            error_log('خطأ في حفظ بيانات السلة في جدول الإرسالات: ' . $e->getMessage());
        }
    }

    /**
     * إرسال إشعارات البريد الإلكتروني للطلب من السلة
     */
    private function send_cart_notification($customer_data, $order) {
        try {
            // التحقق من تفعيل إشعارات البريد الإلكتروني
            $email_enabled = get_option('pexlat_form_email_notifications_enabled', 1);
            if (!$email_enabled) {
                return;
            }

            // الحصول على إعدادات البريد الإلكتروني
            $admin_email = get_option('pexlat_form_admin_email', get_option('admin_email'));
            $email_subject = get_option('pexlat_form_email_subject', 'طلب جديد من الموقع');

            if (empty($admin_email)) {
                return;
            }

        // بناء رسالة البريد الإلكتروني
        $message = sprintf('<h2>تم استلام طلب جديد من السلة #%s</h2>', $order->get_id());
        $message .= sprintf('<p><strong>التاريخ:</strong> %s</p>', $order->get_date_created()->format('Y-m-d H:i:s'));
        $message .= sprintf('<p><strong>الإجمالي:</strong> %s</p>', wc_price($order->get_total()));

        // تفاصيل المنتجات
        $items = $order->get_items();
        $message .= '<h3>تفاصيل الطلب</h3>';
        $message .= '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">';
        $message .= '<tr><th>المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th></tr>';

        foreach ($items as $item) {
            $message .= '<tr>';
            $message .= '<td>' . $item->get_name() . '</td>';
            $message .= '<td>' . $item->get_quantity() . '</td>';
            $message .= '<td>' . wc_price($item->get_subtotal() / $item->get_quantity()) . '</td>';
            $message .= '<td>' . wc_price($item->get_subtotal()) . '</td>';
            $message .= '</tr>';
        }
        $message .= '</table>';

        // معلومات العميل
        $message .= '<h3>معلومات العميل</h3>';
        $message .= '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">';

        // قائمة الحقول التقنية والمعلومات الأساسية التي يجب إخفاؤها من الإشعارات
        // لأن المعلومات الأساسية تظهر بالفعل في عنوان الفاتورة والشحن
        $hidden_fields = array(
            'form_id', 'action', 'nonce', 'product_id', 'variation_id', 'quantity',
            'wp_http_referer', 'shipping_cost', 'shipping_method',
            'product_price', 'original_price', 'product_in_stock',
            'shipping_method_option', 'shipping_method_name', 'shipping_method_title',
            // Basic customer info that appears in billing/shipping addresses
            'full_name', 'first_name', 'last_name', 'phone', 'address',
            'state', 'municipality', 'municipality_text', 'city'
        );

        foreach ($customer_data as $key => $value) {
            if (!empty($value) && !in_array($key, $hidden_fields)) {
                $label = ucfirst(str_replace('_', ' ', $key));
                $message .= '<tr>';
                $message .= '<td style="font-weight: bold;">' . $label . '</td>';
                $message .= '<td>' . esc_html($value) . '</td>';
                $message .= '</tr>';
            }
        }
        $message .= '</table>';

            // إرسال البريد الإلكتروني
            $headers = array('Content-Type: text/html; charset=UTF-8');
            $result = wp_mail($admin_email, $email_subject, $message, $headers);

            if (!$result) {
                error_log('فشل في إرسال إشعار البريد الإلكتروني للطلب من السلة: ' . $order->get_id());
            }

        } catch (Exception $e) {
            error_log('خطأ في إرسال إشعار البريد الإلكتروني للطلب من السلة: ' . $e->getMessage());
        }
    }

    /**
     * التحقق من صحة طلب السلة (العملاء المحظورين والطلبات المتكررة)
     *
     * @return bool|string true إذا كان الطلب صحيح، أو رسالة خطأ
     */
    private function validate_cart_order() {
        // الحصول على بيانات العميل من POST
        $phone_number = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
        $ip_address = $_SERVER['REMOTE_ADDR'];
        $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';

        // التحقق من العميل المحظور
        $blocked_customer = $this->check_blocked_customer($phone_number, $ip_address, $email);

        if ($blocked_customer) {
            $reason = !empty($blocked_customer['reason']) ? $blocked_customer['reason'] : 'تم حظر هذا العميل من تقديم الطلبات.';
            return '<div class="blocked-message">⛔ ' . esc_html($reason) . '</div>';
        }

        // التحقق من تقييد الطلبات
        $order_limit_check = $this->check_order_limit($ip_address);
        if ($order_limit_check !== true) {
            return $order_limit_check;
        }

        return true;
    }

    /**
     * التحقق من العميل المحظور
     *
     * @param string $phone_number رقم الهاتف
     * @param string $ip_address عنوان IP
     * @param string $email البريد الإلكتروني
     * @return array|false مصفوفة تحتوي على بيانات العميل المحظور أو false إذا لم يكن محظوراً
     */
    private function check_blocked_customer($phone_number = '', $ip_address = '', $email = '') {
        global $wpdb;
        $blocked_table = $wpdb->prefix . 'pexlat_form_blocked_customers';

        // التأكد من وجود معيار واحد على الأقل للبحث
        if (empty($phone_number) && empty($ip_address) && empty($email)) {
            return false;
        }

        // بناء استعلامات منفصلة للبحث عن كل معيار
        $queries = array();

        // البحث عن رقم الهاتف إذا كان متوفرًا
        if (!empty($phone_number)) {
            $phone_query = "SELECT * FROM {$blocked_table} WHERE phone_number = %s AND status = %s LIMIT 1";
            $phone_params = array($phone_number, 'active');
            $queries[] = array(
                'query' => $phone_query,
                'params' => $phone_params
            );
        }

        // البحث عن عنوان IP إذا كان متوفرًا
        if (!empty($ip_address)) {
            $ip_query = "SELECT * FROM {$blocked_table} WHERE ip_address = %s AND status = %s LIMIT 1";
            $ip_params = array($ip_address, 'active');
            $queries[] = array(
                'query' => $ip_query,
                'params' => $ip_params
            );
        }

        // البحث عن البريد الإلكتروني إذا كان متوفرًا
        if (!empty($email)) {
            $email_query = "SELECT * FROM {$blocked_table} WHERE email = %s AND status = %s LIMIT 1";
            $email_params = array($email, 'active');
            $queries[] = array(
                'query' => $email_query,
                'params' => $email_params
            );
        }

        // تنفيذ الاستعلامات واحدًا تلو الآخر
        foreach ($queries as $query_data) {
            $prepared_query = $wpdb->prepare($query_data['query'], $query_data['params']);
            $blocked_customer = $wpdb->get_row($prepared_query, ARRAY_A);

            // إذا وجدنا عميلًا محظورًا، نعيده فورًا
            if ($blocked_customer) {
                return $blocked_customer;
            }
        }

        // لم يتم العثور على عميل محظور
        return false;
    }

    /**
     * التحقق من تقييد الطلبات خلال الفترة المحددة
     *
     * @param string $ip_address عنوان IP للمستخدم
     * @return bool|string true إذا كان مسموح بالطلب، أو رسالة خطأ إذا كان هناك تقييد
     */
    private function check_order_limit($ip_address = '') {
        // التحقق من تفعيل خاصية تقييد الطلبات
        $limit_orders_enabled = get_option('pexlat_form_limit_orders_enabled', 1);

        if (!$limit_orders_enabled) {
            return true; // الخاصية غير مفعلة، السماح بالطلب
        }

        // الحصول على إعدادات تقييد الطلبات
        $max_orders = intval(get_option('pexlat_form_max_orders', 3));
        $time_period = intval(get_option('pexlat_form_time_period', 24));
        $time_period_seconds = $time_period * HOUR_IN_SECONDS;

        // الحصول على سجل الطلبات السابقة
        $orders_history = get_transient('orders_history_' . $ip_address);

        if (!$orders_history || !is_array($orders_history)) {
            // لا يوجد سجل سابق، إنشاء سجل جديد
            $orders_history = array(time());
            set_transient('orders_history_' . $ip_address, $orders_history, $time_period_seconds);
            return true;
        }

        // تنظيف السجل من الطلبات القديمة (أكثر من الفترة المحددة)
        $current_time = time();
        $cutoff_time = $current_time - $time_period_seconds;
        $recent_orders = array();

        foreach ($orders_history as $order_time) {
            if ($order_time > $cutoff_time) {
                $recent_orders[] = $order_time;
            }
        }

        // التحقق من عدد الطلبات خلال الفترة المحددة
        if (count($recent_orders) < $max_orders) {
            // إضافة الطلب الحالي إلى السجل
            $recent_orders[] = $current_time;
            set_transient('orders_history_' . $ip_address, $recent_orders, $time_period_seconds);
            return true;
        }

        // تجاوز الحد الأقصى، إنشاء رسالة خطأ
        $time_remaining = $time_period_seconds - ($current_time - min($recent_orders));
        $hours_remaining = ceil($time_remaining / HOUR_IN_SECONDS);

        $base_message = 'يرجى المحاولة مرة أخرى بعد';
        if (function_exists('pexlat_form_translate_text')) {
            $base_message = pexlat_form_translate_text($base_message);
        }

        $time_remaining_formatted = $hours_remaining . ' ساعة';
        if ($hours_remaining == 1) {
            $time_remaining_formatted = 'ساعة واحدة';
        }

        if ($max_orders == 1) {
            $error_message = '<div class="blocked-message">⛔ ' . $base_message . ' ' . $time_remaining_formatted . '</div>';
        } else {
            $max_orders_message = 'عذراً، لقد وصلت للحد الأقصى من الطلبات المسموح بها';
            if (function_exists('pexlat_form_translate_text')) {
                $max_orders_message = pexlat_form_translate_text($max_orders_message);
            }
            $error_message = '<div class="blocked-message">⛔ ' . $max_orders_message . ' (' . $max_orders . '). ' . $base_message . ' ' . $time_remaining_formatted . '</div>';
        }

        return $error_message;
    }

    /**
     * تحديث سجل الطلبات بعد نجاح الطلب
     *
     * @param string $ip_address عنوان IP للمستخدم
     */
    private function update_order_history($ip_address) {
        // التحقق من تفعيل خاصية تقييد الطلبات
        $limit_orders_enabled = get_option('pexlat_form_limit_orders_enabled', 1);

        if (!$limit_orders_enabled) {
            return; // الخاصية غير مفعلة
        }

        // الحصول على إعدادات تقييد الطلبات
        $time_period = intval(get_option('pexlat_form_time_period', 24));
        $time_period_seconds = $time_period * HOUR_IN_SECONDS;

        // الحصول على سجل الطلبات السابقة
        $orders_history = get_transient('orders_history_' . $ip_address);

        if (!$orders_history || !is_array($orders_history)) {
            $orders_history = array();
        }

        // إضافة الطلب الحالي إلى السجل
        $orders_history[] = time();

        // تنظيف السجل من الطلبات القديمة
        $current_time = time();
        $cutoff_time = $current_time - $time_period_seconds;
        $recent_orders = array();

        foreach ($orders_history as $order_time) {
            if ($order_time > $cutoff_time) {
                $recent_orders[] = $order_time;
            }
        }

        // حفظ السجل المحدث
        set_transient('orders_history_' . $ip_address, $recent_orders, $time_period_seconds);
    }

    /**
     * البحث عن طلب مسودة موجود أو إنشاء طلب جديد للسلة
     *
     * @param array $cart محتويات السلة
     * @return WC_Order|false الطلب الموجود أو الجديد
     */
    private function find_or_create_cart_order($cart) {
        if (empty($cart)) {
            return false;
        }

        // الحصول على بيانات العميل من أول منتج في السلة
        $first_item = reset($cart);
        $customer_data = $first_item['customer_data'] ?? array();
        $ip_address = $_SERVER['REMOTE_ADDR'];

        // البحث عن طلب مسودة موجود
        $existing_order = null;

        if (!empty($customer_data['phone'])) {
            // البحث بناءً على رقم الهاتف وعنوان IP
            $draft_key = 'pexlat_form_draft_' . md5($customer_data['phone'] . '_' . $ip_address);
            $existing_draft = get_option($draft_key);

            if ($existing_draft && isset($existing_draft['order_id'])) {
                $existing_order = wc_get_order($existing_draft['order_id']);

                // التحقق من صحة الطلب
                if (!$existing_order || $existing_order->get_status() !== 'draft') {
                    delete_option($draft_key);
                    $existing_order = null;
                } else {
                    // التحقق من أن الطلب حديث (أقل من 24 ساعة)
                    $order_date = $existing_order->get_date_created();
                    if ($order_date && $order_date->getTimestamp() < (time() - 24 * HOUR_IN_SECONDS)) {
                        wp_delete_post($existing_order->get_id(), true);
                        delete_option($draft_key);
                        $existing_order = null;
                    }
                }
            }
        }

        // إذا وجدنا طلب مسودة صالح، نحدثه
        if ($existing_order) {
            // مسح المنتجات الحالية
            $items = $existing_order->get_items();
            foreach ($items as $item_id => $item) {
                $existing_order->remove_item($item_id);
            }

            // تحديث حالة الطلب إلى "بانتظار الدفع" بدلاً من "مسودة"
            $existing_order->set_status('pending');

            return $existing_order;
        }

        // إنشاء طلب جديد إذا لم نجد طلب مسودة
        return wc_create_order();
    }
}

// تهيئة الصنف
new Custom_Cart_System();
